# Backend Preview Testing Guide

This document explains how to test the backend preview functionality for Flight Landing Pages.

## Feature Overview

The backend preview functionality shows template page information and available destination pairs in the TYPO3 backend page layout view for Flight Landing pages (doktype 201). When a Flight Landing page has a template page selected, the preview will display:

**Template Page Information:**
- Template page title and UID
- Whether the template page is a valid Flight Template Page (doktype 200)
- Warning if the template page is hidden
- Warning if the referenced page is not a Flight Template Page

**Destination Pairs Information:**
- List of all active flight routes (destination pairs) configured for this landing page
- Origin and destination codes with full names
- Route slugs for each destination pair
- Count of available routes
- Warning if no destination pairs are configured

## Testing Steps

### 1. Create Test Pages

1. **Create a Flight Template Page (doktype 200):**
   - Go to the TYPO3 backend
   - Create a new page
   - Set the page type to "Flight Template Page" (doktype 200)
   - Give it a title like "Flight Template - Berlin to Paris"
   - Add some content elements with flight placeholders

2. **Create a Flight Landing Page (doktype 201):**
   - Create another new page
   - Set the page type to "Flight Landing Page" (doktype 201)
   - Give it a title like "Landing Page - Berlin to Paris"
   - In the "Template Page" field, select the Flight Template Page created above

3. **Create Flight Route Records:**
   - Go to the "List" module
   - Navigate to a storage folder or the landing page itself
   - Create new "Flight Route" records with different origin/destination pairs:
     - BER → SOF (Berlin to Sofia)
     - BER → VIE (Berlin to Vienna)
     - BER → PRG (Berlin to Prague)
   - Set the "Landing Page" field to point to your Flight Landing Page
   - Make sure "Is Active" is checked for all routes

### 2. View the Backend Preview

1. Navigate to the Flight Landing Page in the backend
2. Go to the "Web > Page" module (page layout view)
3. You should see two information boxes at the top of the page content area:

**Template Page Box (Blue):**
   - Template page title and UID
   - A green checkmark indicating it's a valid template page
   - No warnings (if everything is configured correctly)

**Destination Pairs Box (Blue):**
   - "Available Destination Pairs" heading with a count badge
   - "Add Pairs" button on the right side for adding new destination pairs
   - Compact single-row list of all configured routes showing:
     - **Action buttons**: Button group with preview (eye) and edit (open) icons
     - **Airport codes**: Origin → Destination badges (e.g., BER → SOF)
     - **Location names**: Full names (e.g., Berlin → Sofia)
     - **Route slug**: With link icon (e.g., 🔗 /from-BER-to-SOF)
     - All information displayed horizontally with proper spacing

### 3. Test Warning Scenarios

#### Test Hidden Template Page Warning:
1. Edit the Flight Template Page
2. Check the "Hide" checkbox
3. Save the page
4. Go back to the Flight Landing Page layout view
5. You should see a warning that the template page is hidden

#### Test Invalid Template Page Warning:
1. Create a regular page (doktype 1)
2. Edit the Flight Landing Page
3. Change the template page reference to point to the regular page
4. Save and view the layout
5. You should see a warning that the referenced page is not a Flight Template Page

#### Test No Destination Pairs Warning:
1. Delete or deactivate all flight route records for the landing page
2. Go back to the Flight Landing Page layout view
3. You should see a warning box stating "No Destination Pairs" with a message about configuring flight routes
4. You should see an "Add First Pair" button that opens the record creation form

### 4. Test Add Pairs Functionality

#### Test Add Pairs Button:
1. Navigate to a Flight Landing Page with existing destination pairs
2. Click the "Add Pairs" button in the destination pairs section
3. You should be redirected to the record creation form for flight routes
4. The "Landing Page" field should be pre-filled with the current landing page UID
5. The "Is Active" checkbox should be pre-checked
6. After saving, you should return to the page layout view

#### Test Add First Pair Button:
1. Navigate to a Flight Landing Page with no destination pairs
2. Click the "Add First Pair" button in the warning section
3. You should be redirected to the record creation form for flight routes
4. The form should behave the same as the "Add Pairs" button

### 5. Test Preview Functionality

#### Test Preview Links:
1. Navigate to a Flight Landing Page with existing destination pairs
2. Each route should show an eye icon at the beginning of the row
3. Click the eye icon for any route
4. A new tab should open with the frontend preview of that specific route
5. The URL should be fully qualified (e.g., https://yoursite.com/from-BER-to-SOF)
6. The URL should be based on the current site's base URL

#### Test Site-Aware URLs:
1. If you have multiple sites, test moving a landing page between sites
2. The preview URLs should automatically update to reflect the new site's base URL
3. Preview links should always work regardless of which site the page belongs to

### 6. Test Edit Functionality

#### Test Edit Links:
1. Navigate to a Flight Landing Page with existing destination pairs
2. Each route should show a button group with preview (eye) and edit (open) icons
3. The buttons should be grouped together using TYPO3's standard button group styling
4. The edit icon should use TYPO3's standard `actions-open` icon (same as other edit buttons in TYPO3)
5. Click the edit icon (second button) for any route
6. You should be redirected to the TYPO3 record edit form for that specific flight route
7. The form should be pre-populated with the current route data
8. After saving changes, you should return to the page layout view
9. The updated information should be reflected in the destination pairs list

#### Test Edit Form Functionality:
1. Click the edit icon for a route (e.g., BER → SOF)
2. Modify the destination code (e.g., change SOF to VIE)
3. Update the destination name (e.g., change Sofia to Vienna)
4. Save the record
5. Return to the page layout view
6. Verify the route now shows BER → VIE with updated names
7. Verify the route slug has been automatically updated to reflect the changes

## Expected Behavior

**Template Page Section:**
- **Valid Configuration**: Blue info box with template page information and green checkmark
- **Hidden Template**: Blue info box with warning icon and "Template page is hidden" message
- **Invalid Template**: Blue info box with warning icon and "Not a Flight Template Page" message
- **No Template Selected**: No preview box shown

**Destination Pairs Section:**
- **With Routes**: Blue info box showing "Available Destination Pairs" with count, route list, and "Add Pairs" button
- **No Routes**: Yellow warning box showing "No Destination Pairs" with configuration message and "Add First Pair" button
- **Error Loading Routes**: No destination pairs section shown (graceful degradation)

**Add Pairs Functionality:**
- **Button Behavior**: Clicking any "Add" button opens the TYPO3 record creation form
- **Pre-filled Values**: Landing page UID and "Is Active" checkbox are automatically set
- **Return URL**: After saving, user returns to the page layout view

**Preview Functionality:**
- **Preview Icon**: Eye icon appears at the beginning of each route row
- **URL Generation**: Fully qualified URLs based on the current site's base URL
- **Site Awareness**: URLs automatically adapt when pages are moved between sites
- **New Tab**: Preview links open in new tabs to avoid losing backend context

**Edit Functionality:**
- **Button Group**: Preview and edit icons are grouped using TYPO3's standard btn-group styling
- **System Integration**: Buttons use TYPO3's built-in button classes (btn-default, btn-sm)
- **Native Hover Effects**: Uses TYPO3's standard button hover colors and effects
- **Direct Editing**: Clicking edit icon opens the TYPO3 record edit form for that specific route
- **Return URL**: After saving changes, user returns to the page layout view
- **Real-time Updates**: Changes are immediately reflected in the destination pairs list

## Technical Details

The preview is implemented using:
- Event listener: `Bgs\FlightLandingPages\EventListener\PagePreviewEventListener`
- Event: `TYPO3\CMS\Backend\Controller\Event\ModifyPageLayoutContentEvent`
- Template: `EXT:flight_landing_pages/Resources/Private/Templates/Backend/PagePreview.html`
- CSS: `EXT:flight_landing_pages/Resources/Public/CSS/backend.css`
