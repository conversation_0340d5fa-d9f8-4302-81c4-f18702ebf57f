# Virtual Route 404 Troubleshooting Guide

This guide helps resolve 404 errors when accessing virtual route URLs for Flight Landing Pages destination pairs.

## Problem Description

When trying to access a virtual route URL like `/flights/ber-sof`, you get a 404 error instead of the expected flight landing page content.

## Root Cause

The 404 error occurs because TYPO3's routing system doesn't recognize virtual route URLs. The system needs middleware to intercept these URLs before TYPO3's standard page resolution process.

## Solution Implementation

The solution involves implementing a middleware that:
1. **Intercepts virtual route URLs** before TYPO3's page resolver
2. **Validates the route pattern** (e.g., `/flights/ber-sof`)
3. **Finds matching flight routes** in the database
4. **Redirects internally** to the landing page with route data preserved

## Implementation Steps

### Step 1: Verify Middleware Registration

Check that the middleware is properly registered in `Configuration/RequestMiddlewares.php`:

```php
<?php
return [
    'frontend' => [
        'flight-landing-pages/virtual-route' => [
            'target' => \Bgs\FlightLandingPages\Middleware\VirtualRouteMiddleware::class,
            'before' => [
                'typo3/cms-frontend/page-resolver',
            ],
            'after' => [
                'typo3/cms-frontend/site',
            ],
        ],
    ],
];
```

### Step 2: Clear All Caches

After implementing the middleware, clear all TYPO3 caches:

```bash
# Using ddev
ddev typo3 cache:flush

# Or via backend
# Go to Admin Tools > Maintenance > Flush TYPO3 and PHP Caches
```

### Step 3: Verify Database Setup

Ensure your test data is properly configured:

#### Flight Landing Page
- **Page Type**: Flight Landing Page (doktype 201)
- **Slug**: `/flights` (or your desired path)
- **Template Page**: Reference to a Flight Template Page
- **Backend Layout (Subpages)**: Optional but recommended

#### Flight Template Page
- **Page Type**: Flight Template Page (doktype 200)
- **Content**: Content elements with flight placeholders
- **Backend Layout**: Optional fallback layout

#### Flight Routes
- **Landing Page**: Reference to your Flight Landing Page
- **Origin Code**: BER (3-letter code)
- **Destination Code**: SOF (3-letter code)
- **Route Slug**: ber-sof (auto-generated)
- **Is Active**: Yes
- **Hidden**: No

### Step 4: Test Virtual Route Access

Try accessing the virtual route:
- **URL**: `https://yoursite.com/flights/ber-sof`
- **Expected**: Landing page content with flight-specific data
- **Actual**: Should no longer show 404

## Debugging Steps

### Debug 1: Check Middleware Execution

Add debug output to the middleware to verify it's being called:

```php
// In VirtualRouteMiddleware::process()
error_log('VirtualRouteMiddleware: Processing path: ' . $path);
```

### Debug 2: Verify Route Pattern Matching

Check if the URL pattern is being recognized:

```php
// In VirtualRouteMiddleware::matchVirtualRoute()
error_log('Checking route pattern: ' . $routeSlug);
if (preg_match('/^([a-z]{3})-([a-z]{3})$/i', $routeSlug, $matches)) {
    error_log('Pattern matched: ' . $originCode . ' -> ' . $destinationCode);
}
```

### Debug 3: Verify Database Queries

Check if flight routes are found:

```php
// In VirtualRouteMiddleware::findFlightRoute()
error_log('Looking for route: ' . $originCode . ' -> ' . $destinationCode . ' on page ' . $landingPageUid);
```

### Debug 4: Check Site Context

Verify that the landing page belongs to the current site:

```php
// In VirtualRouteMiddleware::isPageInSite()
error_log('Checking if page ' . $pageUid . ' belongs to site root ' . $siteRootPageId);
```

## Common Issues and Solutions

### Issue 1: Middleware Not Executing
**Symptoms**: No debug output from middleware
**Solutions**:
- Clear all caches
- Check middleware registration syntax
- Verify file path and class name
- Restart web server if using ddev

### Issue 2: Route Pattern Not Matching
**Symptoms**: Middleware executes but doesn't find matches
**Solutions**:
- Verify URL format: `/flights/ber-sof` (3-letter codes)
- Check case sensitivity (codes should be uppercase in database)
- Ensure route slug format matches pattern

### Issue 3: Landing Page Not Found
**Symptoms**: Route pattern matches but no landing page found
**Solutions**:
- Verify landing page slug matches URL path
- Check that landing page is not hidden
- Ensure landing page belongs to correct site
- Verify doktype is 201

### Issue 4: Flight Route Not Found
**Symptoms**: Landing page found but no flight route
**Solutions**:
- Check flight route exists in database
- Verify origin/destination codes match URL
- Ensure flight route is active (is_active = 1)
- Check that route belongs to correct landing page

### Issue 5: Site Context Issues
**Symptoms**: Works on one site but not others
**Solutions**:
- Verify site configuration
- Check page tree structure
- Ensure flight routes are in correct site context

## Testing Checklist

- [ ] Middleware is registered in RequestMiddlewares.php
- [ ] All caches are cleared
- [ ] Flight Landing Page exists with correct slug
- [ ] Flight Template Page exists and is referenced
- [ ] Flight Route exists with correct codes and is active
- [ ] URL pattern matches: `/flights/ber-sof`
- [ ] Debug logging shows middleware execution
- [ ] Database queries return expected results

## Manual Testing Commands

### Check Database Records

```sql
-- Check Flight Landing Pages
SELECT uid, title, slug, doktype FROM pages WHERE doktype = 201;

-- Check Flight Template Pages  
SELECT uid, title, slug, doktype FROM pages WHERE doktype = 200;

-- Check Flight Routes
SELECT * FROM tx_landingpages_domain_model_flightroute 
WHERE origin_code = 'BER' AND destination_code = 'SOF';
```

### Test URL Patterns

```bash
# Test direct landing page access (should work)
curl -I https://yoursite.com/flights

# Test virtual route access (should work after fix)
curl -I https://yoursite.com/flights/ber-sof

# Test invalid pattern (should return 404)
curl -I https://yoursite.com/flights/invalid
```

## Expected Behavior After Fix

1. **Direct Landing Page Access** (`/flights`):
   - Returns 200 OK
   - Shows normal landing page content
   - `{flightRouteData.isVirtualRoute}` = false

2. **Virtual Route Access** (`/flights/ber-sof`):
   - Returns 200 OK
   - Shows template page content with flight data
   - `{flightRouteData.isVirtualRoute}` = true
   - Placeholders are replaced with flight-specific data

3. **Invalid Routes** (`/flights/invalid`):
   - Returns 404 Not Found
   - No middleware intervention

## Performance Considerations

- Middleware adds minimal overhead to non-matching requests
- Database queries are optimized with proper indexes
- Consider caching for frequently accessed routes
- Monitor performance in production environments

## Next Steps

After resolving the 404 issue:
1. Test all virtual route functionality
2. Verify placeholder replacement works
3. Check backend layout application
4. Test with different flight routes
5. Verify multi-site compatibility if applicable
