<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:core="http://typo3.org/ns/TYPO3/CMS/Core/ViewHelpers"
    data-namespace-typo3-fluid="true"
>

<f:layout name="Module" />

<f:section name="Content">

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <f:translate key="LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:pages.import.title" />
                    </h3>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        <f:translate key="LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:pages.import.description" />
                    </p>

                    <f:if condition="{errorMessage}">
                        <div class="alert alert-danger" role="alert">
                            <strong>Import Error:</strong> {errorMessage}
                        </div>
                    </f:if>

                    <form action="{importUrl}" method="post" enctype="multipart/form-data" class="form-horizontal">
                        <input type="hidden" name="landingPageId" value="{landingPageId}" />
                        <input type="hidden" name="returnUrl" value="{returnUrl}" />

                        <div class="form-group row">
                            <label for="csvFile" class="col-sm-3 col-form-label">
                                <f:translate key="LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:pages.import.file_label" />
                            </label>
                            <div class="col-sm-9">
                                <input type="file" id="csvFile" name="csvFile" accept=".csv" class="form-control" required />
                                <small class="form-text text-muted">
                                    Accepted file types: .csv
                                </small>
                            </div>
                        </div>

                        <div class="form-group row">
                            <div class="col-sm-9 offset-sm-3">
                                <button type="submit" class="btn btn-primary">
                                    <core:icon identifier="actions-upload" size="small" />
                                    <f:translate key="LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:pages.import.submit_button" />
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">CSV Format</h4>
                </div>
                <div class="card-body">
                    <p><strong>Required columns:</strong></p>
                    <ul class="list-unstyled">
                        <li>• Origin Code</li>
                        <li>• Origin Name</li>
                        <li>• Origin Type</li>
                        <li>• Destination Code</li>
                        <li>• Destination Name</li>
                        <li>• Destination Type</li>
                        <li>• Route Slug</li>
                        <li>• Is Active</li>
                    </ul>
                    <p class="text-muted">
                        <small>Use the Export CSV function to see the expected format.</small>
                    </p>
                    <p class="text-warning">
                        <small><strong>Important:</strong> The CSV file must not contain duplicate routes (same origin type, origin code, destination type, and destination code). Import will fail if duplicates are found.</small>
                    </p>
                </div>
            </div>
        </div>
    </div>

</f:section>

</html>
