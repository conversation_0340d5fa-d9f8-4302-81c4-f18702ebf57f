<style>
.destinations-menu { list-style: none; padding: 0; margin: 0; }
.destinations-menu li { display: block; margin-bottom: 0.5em; }
.destinations-menu a { text-decoration: none; }
</style>

<f:if condition="{routes}">
    <f:then>
        <ul class="destinations-menu">
            <f:for each="{routes}" as="route">
                <li>
                    <a href="{route.fullUrl}">
                        {route.originName} → {route.destinationName}
                    </a>
                </li>
            </f:for>
        </ul>
    </f:then>
    <f:else>
        <div class="no-routes">
            {f:translate(key: 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang.xlf:no_routes_available', default: 'No routes available')}
        </div>
    </f:else>
</f:if>
