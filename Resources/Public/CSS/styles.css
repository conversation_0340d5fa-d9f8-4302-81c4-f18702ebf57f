/* Landing Pages Extension Styles */

/* Basic page layout */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    background-color: #f8f9fa;
    padding: 20px 0;
    margin-bottom: 30px;
    border-bottom: 1px solid #dee2e6;
}

header h1 {
    margin: 0;
    color: #2c3e50;
}

.main-navigation ul {
    list-style: none;
    padding: 0;
    margin: 10px 0 0 0;
}

.main-navigation li {
    display: inline-block;
    margin-right: 20px;
}

.main-navigation a {
    text-decoration: none;
    color: #3498db;
    padding: 5px 10px;
    border-radius: 3px;
    transition: all 0.2s;
}

.main-navigation a:hover,
.main-navigation .active a {
    background-color: #3498db;
    color: white;
}

main {
    min-height: 400px;
    margin-bottom: 30px;
}

footer {
    background-color: #f8f9fa;
    padding: 20px 0;
    text-align: center;
    border-top: 1px solid #dee2e6;
    color: #7f8c8d;
}

.landing-pages {
    font-family: Arial, sans-serif;
}

/* Flight Page Styles */
.flight-page {
    margin: 20px 0;
}

.flight-route-header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.route-info {
    font-size: 1.2em;
    color: #7f8c8d;
    margin-bottom: 20px;
}

.route-info .origin,
.route-info .destination {
    font-weight: bold;
    color: #3498db;
}

.flight-details {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

.price-info {
    text-align: center;
    margin-bottom: 20px;
}

.price-info .price {
    font-size: 2em;
    font-weight: bold;
    color: #e74c3c;
}

.price-info .airline {
    display: block;
    color: #7f8c8d;
    margin-top: 5px;
}

.flight-times,
.dates {
    display: flex;
    justify-content: space-around;
    margin: 15px 0;
}

.flight-times > div,
.dates > div {
    text-align: center;
}

/* Flight Reference List Styles */
.flight-reference-list {
    margin: 20px 0;
}

/* Search Filter Styles */
.flight-search-filter {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.search-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.search-group label {
    font-weight: bold;
    color: #2c3e50;
    font-size: 1.1em;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.route-search-input {
    padding: 12px 16px;
    padding-right: 40px; /* Make room for clear button */
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    font-size: 1em;
    transition: border-color 0.3s ease;
    background: white;
    width: 100%;
}

.route-search-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.clear-search-btn {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    font-size: 20px;
    color: #7f8c8d;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 50%;
    transition: background-color 0.2s ease, color 0.2s ease;
    line-height: 1;
}

.clear-search-btn:hover {
    background-color: #e9ecef;
    color: #495057;
}

.clear-search-btn:focus {
    outline: none;
    background-color: #e9ecef;
}

.search-results-info {
    margin-top: 8px;
}

.results-count {
    color: #7f8c8d;
    font-size: 0.9em;
    font-style: italic;
}

.flight-filters {
    background: #ecf0f1;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
}

.filter-group label {
    font-weight: bold;
    margin-bottom: 5px;
}

.filter-group select {
    padding: 8px;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
}

/* Grid Layout */
.flight-routes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.route-card {
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.route-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.route-header h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
}

.route-codes {
    font-size: 1.1em;
    font-weight: bold;
    color: #3498db;
    margin: 10px 0;
}

.route-types {
    color: #7f8c8d;
    font-size: 0.9em;
    margin: 10px 0;
}

.route-action {
    margin-top: 15px;
}

/* Cards Layout */
.flight-routes-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: 20px 0;
}

.route-card-large {
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    flex: 1;
    min-width: 300px;
    max-width: 400px;
}

.card-title {
    color: #2c3e50;
    margin-bottom: 15px;
}

.card-text {
    color: #7f8c8d;
    line-height: 1.5;
    margin-bottom: 20px;
}

/* List Layout */
.flight-routes-list {
    margin: 20px 0;
}

.route-list {
    list-style: none;
    padding: 0;
}

.route-item {
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    padding: 15px 20px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s;
}

.route-item:hover {
    background-color: #f8f9fa;
}

.route-name {
    font-weight: bold;
    color: #2c3e50;
}

.route-codes {
    color: #7f8c8d;
    font-size: 0.9em;
    margin-left: 10px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    text-align: center;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
    color: white;
    text-decoration: none;
}



/* Alert Styles */
.alert {
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
}

.alert-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

/* No Routes Message */
.no-routes {
    text-align: center;
    padding: 40px;
    color: #7f8c8d;
}

/* Responsive Design */
@media (max-width: 768px) {
    .flight-times,
    .dates {
        flex-direction: column;
        gap: 10px;
    }

    .flight-search-filter {
        padding: 15px;
    }

    .route-search-input {
        padding: 10px 12px;
        padding-right: 40px; /* Keep room for clear button */
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .clear-search-btn {
        right: 6px;
        font-size: 18px;
    }

    .flight-filters {
        flex-direction: column;
    }

    .flight-routes-grid {
        grid-template-columns: 1fr;
    }

    .route-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
