/* Backend styles for Flight Landing Pages */

.flight-landing-page-preview {
    margin: 15px 0;
}

.flight-landing-page-preview .alert {
    border-radius: 4px;
    padding: 12px 15px;
    margin-bottom: 0;
}

.flight-landing-page-preview .media {
    display: flex;
    align-items: flex-start;
}

.flight-landing-page-preview .media-left {
    margin-right: 10px;
    flex-shrink: 0;
}

.flight-landing-page-preview .media-body {
    flex: 1;
}

.flight-landing-page-preview .media-body h5 {
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 600;
}

.flight-landing-page-preview .media-body p {
    margin-bottom: 5px;
    font-size: 13px;
}

.flight-landing-page-preview .media-body small {
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.flight-landing-page-preview .text-success {
    color: #28a745;
}

.flight-landing-page-preview .text-warning {
    color: #ffc107;
}

.flight-landing-page-preview .text-muted {
    color: #6c757d;
}

/* Flight Routes List Styles */
.flight-landing-page-preview .flight-routes-list {
    margin-top: 15px;
}

.flight-landing-page-preview .flight-route-item {
    padding: 4px 0;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.flight-landing-page-preview .flight-route-item:last-child {
    border-bottom: none;
}

/* Inactive route styling */
.flight-landing-page-preview .flight-route-item.route-inactive {
    opacity: 0.6;
    background-color: #f8f9fa;
}

.flight-landing-page-preview .flight-route-item.route-inactive .route-codes .badge {
    opacity: 0.7;
}

.flight-landing-page-preview .flight-route-item.route-inactive .route-names {
    color: #6c757d;
}

.flight-landing-page-preview .flight-route-item.route-inactive .route-slug {
    opacity: 0.7;
}

/* Backend Search Filter Styles */
.flight-landing-page-preview .backend-search-filter {
    /* No background or border for more compact look */
}

.flight-landing-page-preview .backend-search-input {
    width: 160px;
    flex-shrink: 0;
}

.flight-landing-page-preview .backend-search-filter .form-control {
    font-size: 12px;
    height: 28px;
    padding: 4px 8px;
}

.flight-landing-page-preview .backend-search-filter .form-control:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 1px rgba(0, 124, 186, 0.25);
}

.flight-landing-page-preview .backend-search-filter .btn {
    height: 28px;
    padding: 4px 8px;
    font-size: 14px;
    line-height: 1;
}

.flight-landing-page-preview #backend-results-count {
    font-style: italic;
    color: #6c757d;
    font-size: 11px;
}

/* Hidden routes during search */
.flight-landing-page-preview .flight-route-item.search-hidden {
    display: none !important;
}

/* Route Actions Container */
.flight-landing-page-preview .route-actions {
    flex-shrink: 0;
}

/* Button group styling for route actions */
.flight-landing-page-preview .route-actions .btn-group {
    display: inline-flex;
}

/* Enhanced button styling for route actions */
.flight-landing-page-preview .route-actions .btn {
    padding: 4px 8px;
    line-height: 1;
    border-radius: 0;
    transition: all 0.15s ease-in-out;
}

.flight-landing-page-preview .route-actions .btn:first-child {
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
}

.flight-landing-page-preview .route-actions .btn:last-child {
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}

/* Hover effects for button groups - use TYPO3's default hover styling */
.flight-landing-page-preview .route-actions .btn:hover {
    z-index: 2;
}

.flight-landing-page-preview .route-codes {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.flight-landing-page-preview .route-arrow {
    color: #6c757d;
    display: flex;
    align-items: center;
    margin: 0 2px;
}

.flight-landing-page-preview .route-names {
    font-size: 13px;
    color: #6c757d;
    flex-grow: 1;
    min-width: 180px;
    font-weight: 500;
}

.flight-landing-page-preview .route-slug {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
}

.flight-landing-page-preview .route-slug small {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
}

.flight-landing-page-preview .badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 3px;
}

/* Fixed width for airport code badges to ensure vertical alignment */
.flight-landing-page-preview .route-codes .badge {
    min-width: 40px;
    text-align: center;
    display: inline-block;
}

/* Type-specific badge colors for visual distinction */
/* Airport badges - Green family (keeping current style) */
.flight-landing-page-preview .badge-airport-origin {
    background-color: oklch(0.5 0.1812 259.58);
    color: white;
}

.flight-landing-page-preview .badge-airport-destination {
    background-color: oklch(0.6 0.2 259.58);
    color: white;
}

/* City badges - Blue family */
.flight-landing-page-preview .badge-city-origin {
    background-color: oklch(0.6 0.1751 146.74);
    color: white;
}

.flight-landing-page-preview .badge-city-destination {
    background-color: oklch(0.65 0.1751 146.74);
    color: white;
}

/* Country badges - Purple family */
.flight-landing-page-preview .badge-country-origin {
    background-color: oklch(0.5 0.2251 320);
    color: white;
}

.flight-landing-page-preview .badge-country-destination {
    background-color: oklch(0.6 0.2251 320);
    color: white;
}

.flight-landing-page-preview .badge-primary {
    background-color: #007bff;
    color: white;
}

.flight-landing-page-preview .badge-success {
    background-color: #28a745;
    color: white;
}

.flight-landing-page-preview .badge-secondary {
    background-color: #6c757d;
    color: white;
}

.flight-landing-page-preview .flight-route-item code {
    background-color: #f8f9fa;
    color: #e83e8c;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 11px;
}

/* Button Styles */
.flight-landing-page-preview .btn {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 3px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.flight-landing-page-preview .btn-primary {
    background-color: #007bff;
    color: white;
}

.flight-landing-page-preview .btn-primary:hover {
    background-color: #0056b3;
    color: white;
    text-decoration: none;
}

.flight-landing-page-preview .btn-success {
    background-color: #28a745;
    color: white;
}

.flight-landing-page-preview .btn-success:hover {
    background-color: #1e7e34;
    color: white;
    text-decoration: none;
}

.flight-landing-page-preview .d-flex {
    display: flex;
}

.flight-landing-page-preview .justify-content-between {
    justify-content: space-between;
}

.flight-landing-page-preview .align-items-center {
    align-items: center;
}

/* Responsive Design for Route Items */
@media (max-width: 768px) {
    .flight-landing-page-preview .flight-route-item {
        gap: 8px;
    }

    .flight-landing-page-preview .route-actions .btn {
        padding: 3px 6px;
        font-size: 11px;
    }

    .flight-landing-page-preview .route-names {
        min-width: 120px;
        font-size: 12px;
    }

    .flight-landing-page-preview .route-slug small {
        font-size: 10px;
    }

    /* Slightly smaller fixed width for mobile */
    .flight-landing-page-preview .route-codes .badge {
        min-width: 36px;
    }
}

@media (max-width: 480px) {
    .flight-landing-page-preview .flight-route-item {
        flex-wrap: wrap;
        gap: 8px;
    }

    .flight-landing-page-preview .route-actions .btn {
        padding: 2px 4px;
        font-size: 10px;
    }

    .flight-landing-page-preview .route-slug {
        width: 100%;
        margin-left: 60px; /* Adjusted for button group */
    }

    /* Even smaller fixed width for very small screens */
    .flight-landing-page-preview .route-codes .badge {
        min-width: 32px;
        font-size: 10px;
        padding: 3px 6px;
    }
}

/* Enhanced Visual Hierarchy */
.flight-landing-page-preview .route-codes .badge {
    font-weight: 600;
    letter-spacing: 0.5px;
}

.flight-landing-page-preview .route-names {
    font-weight: 500;
    line-height: 1.4;
}

/* Backend Pagination Styles */
.flight-landing-page-preview .backend-pagination-container {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
    margin-top: 15px;
}

.flight-landing-page-preview .backend-pagination-info select {
    width: auto;
    min-width: 120px;
    font-size: 12px;
    padding: 4px 20px 4px 8px;
    height: 28px;
    background-color: #fff;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 6px center;
    background-repeat: no-repeat;
    background-size: 12px;
    border: 1px solid #ced4da;
    border-radius: 3px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
}

.flight-landing-page-preview .backend-pagination-info select:hover {
    border-color: #adb5bd;
    background-color: #f8f9fa;
}

.flight-landing-page-preview .backend-pagination-info select:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.flight-landing-page-preview .backend-pagination-info {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Dynamic layout based on items per page visibility */
.flight-landing-page-preview .backend-pagination-container .pagination-layout-center {
    justify-content: center;
}

.flight-landing-page-preview .backend-pagination-container .pagination-layout-between {
    justify-content: space-between;
}

.flight-landing-page-preview .backend-pagination-controls .pagination {
    margin-bottom: 0;
}

.flight-landing-page-preview .backend-pagination-controls .page-link {
    font-size: 12px;
    padding: 4px 8px;
    border-color: #dee2e6;
    color: #495057;
}

.flight-landing-page-preview .backend-pagination-controls .page-link:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
    color: #495057;
}

.flight-landing-page-preview .backend-pagination-controls .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

.flight-landing-page-preview .backend-pagination-controls .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

/* Responsive pagination */
@media (max-width: 768px) {
    .flight-landing-page-preview .backend-pagination-container {
        flex-direction: column;
        gap: 10px;
    }

    .flight-landing-page-preview .backend-pagination-container .d-flex {
        flex-direction: column;
        align-items: stretch;
    }

    .flight-landing-page-preview .backend-pagination-info {
        text-align: center;
        margin-bottom: 10px;
    }

    .flight-landing-page-preview .backend-pagination-controls {
        display: flex;
        justify-content: center;
    }
}

.flight-landing-page-preview .route-slug code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-weight: 500;
}
