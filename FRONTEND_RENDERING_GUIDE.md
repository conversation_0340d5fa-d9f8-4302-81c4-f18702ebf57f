# Frontend Rendering Guide for Flight Landing Pages

This guide explains how Flight Landing Pages now render as **normal TYPO3 pages** using your site's existing templates and configuration.

## Overview

Flight Landing Pages (doktype 201) now render exactly like standard TYPO3 pages with these key improvements:

- ✅ **Uses your site's existing PAGE object configuration**
- ✅ **Renders with your site's templates, layouts, and partials**
- ✅ **Maintains your site's navigation, styling, and functionality**
- ✅ **Adds flight-specific data via `{flightRouteData}` template variable**
- ✅ **No custom rendering or template manipulation**

## What Changed

### Before (Plugin-based Rendering)
- Flight Landing Pages used custom controllers and plugins
- Custom PAGE object overrides for template pages
- Separate template files for flight content
- Plugin-based rendering that bypassed normal TYPO3 page rendering

### After (Standard Page Rendering)
- Flight Landing Pages render as normal TYPO3 pages
- Template pages (doktype 200) render normally
- Flight data is provided via data processor
- No custom PAGE object configuration
- Full integration with existing site templates

## How It Works

### 1. Standard Page Rendering
Flight Landing Pages use TYPO3's standard page rendering process:
1. TYPO<PERSON> loads the page based on the URL
2. The PAGE object configuration from your site is used
3. Your site's templates, layouts, and partials are loaded
4. The FlightRouteProcessor adds flight data to template variables
5. Your templates render normally with additional flight data available

### 2. Flight Data Integration
The `FlightRouteProcessor` data processor:
- Runs only for Flight Landing Pages (doktype 201)
- Detects virtual routes from URLs (e.g., `/flights/ber-sof`)
- Loads flight route data and template page content
- Processes placeholders in template content
- Makes all data available via `{flightRouteData}` template variable

### 3. Template Integration
Your site templates can optionally display flight content:
- Use `{flightRouteData.isVirtualRoute}` to detect virtual routes
- Display flight-specific content when appropriate
- Fall back to normal page content otherwise

## Testing the Changes

### 1. Test Normal Page Access
Visit a Flight Landing Page directly (e.g., `https://yoursite.com/flights`):

**Expected Results:**
- ✅ Page uses your site's normal template and styling
- ✅ Page shows normal content elements
- ✅ Navigation and footer work normally
- ✅ No flight-specific content is shown
- ✅ `{flightRouteData.isVirtualRoute}` is `false`

### 2. Test Virtual Route Access
Visit a virtual route (e.g., `https://yoursite.com/flights/ber-sof`):

**Expected Results:**
- ✅ Page uses your site's template and styling
- ✅ Page shows flight-specific content (if integrated)
- ✅ Navigation and footer still work
- ✅ `{flightRouteData.isVirtualRoute}` is `true`
- ✅ Flight data is available in `{flightRouteData}`

### 3. Test Template Page Access
Visit a Flight Template Page directly (e.g., `https://yoursite.com/flight-templates/berlin-sofia`):

**Expected Results:**
- ✅ Page renders normally as a standard TYPO3 page
- ✅ Content elements are visible
- ✅ No 404 errors or custom restrictions

## Integration Steps

### Step 1: Update Your Site Template (Optional)
Add flight content to your main page template where appropriate:

```html
<!-- In your site's Resources/Private/Templates/Page/Default.html -->
<f:section name="Main">
    <div class="container">
        <!-- Flight content (only shows on virtual routes) -->
        <f:if condition="{flightRouteData.isVirtualRoute}">
            <div class="flight-header">
                <h1>Flights from {flightRouteData.currentFlightRoute.originName} to {flightRouteData.currentFlightRoute.destinationName}</h1>
            </div>
        </f:if>

        <!-- Normal page content -->
        <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '0'}" />
    </div>
</f:section>
```

### Step 2: Use the Provided Partial (Recommended)
Include the flight content partial in your template:

```html
<!-- Flight content (only shows on virtual routes) -->
<f:render partial="FlightContent" arguments="{_all}" />
```

### Step 3: Test Integration
1. Create a Flight Landing Page
2. Add some flight routes
3. Test both normal page access and virtual route access
4. Verify that your site's styling and functionality are preserved

## Available Template Variables

When viewing a Flight Landing Page, these variables are available:

### `{flightRouteData}`
- `isVirtualRoute` (boolean) - Whether this is a virtual route
- `currentFlightRoute` (array) - Current flight route data
- `allFlightRoutes` (array) - All routes for this landing page
- `templatePageContent` (array) - Processed template content
- `flightData` (array) - Flight information for placeholders

### Example Usage
```html
<f:if condition="{flightRouteData.isVirtualRoute}">
    <h1>Flights from {flightRouteData.currentFlightRoute.originName} to {flightRouteData.currentFlightRoute.destinationName}</h1>
    
    <f:for each="{flightRouteData.templatePageContent}" as="contentElement">
        <div class="content-element">
            <f:if condition="{contentElement.header}">
                <h2>{contentElement.header -> f:format.raw()}</h2>
            </f:if>
            <f:if condition="{contentElement.bodytext}">
                <f:format.html>{contentElement.bodytext}</f:format.html>
            </f:if>
        </div>
    </f:for>
</f:if>
```

## Troubleshooting

### Issue: Flight content not showing
**Solution:** Check that you've added flight content integration to your site template

### Issue: Site styling broken
**Solution:** Verify that no custom PAGE object overrides are interfering with your site configuration

### Issue: Virtual routes not working
**Solution:** Check that flight routes are created and active, and that the URL pattern matches

### Issue: Template pages showing 404
**Solution:** Template pages now render normally - check that they're not hidden or deleted

## Migration Notes

If you were using a previous version:
1. Remove any custom PAGE object configurations
2. Update your templates to use the new integration approach
3. Test that normal page functionality is restored
4. Verify that flight functionality works on virtual routes

## Benefits of the New Approach

1. **Better Integration**: Works seamlessly with existing TYPO3 sites
2. **No Conflicts**: Doesn't interfere with your site's configuration
3. **Standard Behavior**: Uses normal TYPO3 page rendering
4. **Easier Maintenance**: No custom rendering logic to maintain
5. **Better Performance**: Leverages TYPO3's built-in caching and optimization
