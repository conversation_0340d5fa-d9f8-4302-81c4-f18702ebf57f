# TYPO3 Landing Pages Extension

A TYPO3 extension for creating landing pages with configurable airport/city/country pairs and multi-site support. **Integrates seamlessly with your existing site configuration without overriding your templates or TypoScript.**

## Features

### ✈️ **Core Features**
- **Route Configuration**: Configure pairs of 'from' - 'to' locations with codes, names, and types (airport/city/country)
- **Route Reference List**: Content element to display all available routes with filtering options
- **Multi-site Support**: Per-site configuration using site identifiers
- **Template System**: Dynamic content with placeholder replacement
- **Custom Page Types**: Template pages (doktype 200) and landing pages (doktype 201)
- **Site Integration**: Works with your existing site templates and TypoScript configuration

### 🎯 **Key Benefits**
- **SEO-Friendly**: Create structured content for routes
- **Scalable**: Easy to add new routes and manage content
- **Flexible**: Multiple display modes (list, grid, cards) and customizable templates
- **Multi-tenant**: Perfect for multi-site TYPO3 installations

## Installation

### Via Composer (Recommended)
```bash
composer require bgs/landing-pages
```

### Manual Installation
1. Download the extension
2. Place it in `packages/landing-pages/`
3. Activate the extension in the Extension Manager

## Site Integration

**Important**: This extension is designed to work **with** your existing site configuration, not replace it.

### Quick Integration

Add route content to your existing site template:

```html
<!-- In your site's page template -->
<f:render partial="RouteContent" arguments="{_all}" />
```

This will automatically show route-specific content when viewing virtual routes (e.g., `/routes/ber-sof`) while preserving your site's normal functionality.

### What the Extension Does

- ✅ **Preserves your site's TypoScript and templates**
- ✅ **Only adds route data processing to Landing Pages (doktype 201)**
- ✅ **Uses your existing page layouts and styling**
- ✅ **Provides route data via `{routeData}` template variable**

### What the Extension Does NOT Do

- ❌ **Does not override your PAGE object configuration**
- ❌ **Does not replace your site's templates or layouts**
- ❌ **Does not interfere with normal pages**

For detailed integration instructions, see [SITE_INTEGRATION.md](SITE_INTEGRATION.md).

## Configuration

### 1. Database Setup
After installation, update the database schema:
- Go to **Admin Tools > Maintenance > Analyze Database Structure**
- Apply the changes for the route table and page extensions

### 2. Create Routes
1. Go to **Web > List** module
2. Create new **Route** records
3. Configure:
   - Origin Code (e.g., "BER")
   - Origin Name (e.g., "Berlin Brandenburg Airport")
   - Origin Type (airport/city/country)
   - Destination Code (e.g., "SOF")
   - Destination Name (e.g., "Sofia Airport")
   - Destination Type (airport/city/country)
   - Route Slug (e.g., "from-BER-to-SOF")

### 3. Add Content Elements
The extension provides two content elements:

#### Landing Page
- Displays route information with dynamic content
- Configurable template files
- Placeholder replacement for dynamic content

#### Route Reference List
- Shows all available routes for the current site
- Multiple display modes: list, grid, cards
- Optional origin/destination filters
- Configurable items per page

## Usage

### Route Management
Once routes are configured, they can be displayed through:
1. Landing page content elements with dynamic content
2. Route reference lists showing all available routes
3. Custom templates with placeholder replacement

### URL Generation
Landing page URLs are generated in the format: **Site Endpoint + Landing Page Path + Pair Slug**

Example: `https://example.com/routes/from-BER-to-SOF`

#### URL Structure Components:
- **Site Endpoint**: The base URL of your TYPO3 site (e.g., `https://example.com`)
- **Landing Page Path**: The slug/path of the landing page (e.g., `routes`)
- **Pair Slug**: The route identifier (e.g., `from-BER-to-SOF`)

The `UrlGenerationService` handles URL creation and provides methods for:
- Generating URLs from Route models
- Building URLs from landing page and route IDs
- Parsing URLs to extract components
- Validating URL components

### Template Placeholders
Templates support placeholders that are automatically replaced by the PlaceholderService:

```html
<h1>Routes from {origin} to {destination}</h1>
<p>Price from: {price} {currency}</p>
<p>Duration: {route.duration}</p>
<p>Departure: {route.departureTime}</p>
```

Available placeholders (based on route data structure):
- `{origin}`, `{destination}` - Basic location names
- `{price}`, `{currency}`, `{airline}` - Pricing and airline info
- `{departureDate}`, `{returnDate}` - Travel dates
- `{route.duration}`, `{route.departureTime}`, `{route.arrivalTime}` - Nested route details

### Multi-site Configuration
For multi-site installations:
1. Routes are automatically associated with sites based on the page tree where they are stored
2. The RouteReferenceController automatically filters routes by the current site context
3. Configure default templates in TypoScript constants:

```typoscript
plugin.tx_landingpages.settings.defaultTemplate = EXT:your_site/Resources/Private/Templates/Route/Custom.html
```

## Development

### Extending the Extension
The extension is built with extensibility in mind:

#### Custom Templates
Create custom templates and reference them in:
- FlexForm configuration (Page plugin)
- TypoScript constants

#### Custom Placeholders
Extend the `PlaceholderService` to add custom placeholders:

```php
class CustomPlaceholderService extends \Bgs\LandingPages\Service\PlaceholderService
{
    public function replacePlaceholders(string $template, array $routeData): string
    {
        // Call parent method first
        $template = parent::replacePlaceholders($template, $routeData);

        // Add custom placeholders
        $template = str_replace('{custom.placeholder}', $customValue, $template);

        return $template;
    }
}
```

#### ViewHelper Usage
Use the ReplacePlaceholderViewHelper in your Fluid templates:

```html
<lp:replacePlaceholder content="{templateContent}" data="{routeData}" />
```

#### Custom Route Data
Extend the `PageController` to integrate with real route APIs by overriding the `getRouteData` method.

## File Structure

```
landing-pages/
├── Classes/
│   ├── Controller/           # PageController, RouteReferenceController
│   ├── Domain/              # Models (Route, LandingPage, TemplatePage) & Repositories
│   ├── Service/             # PlaceholderService, SiteConfigurationService
│   └── ViewHelpers/         # ReplacePlaceholderViewHelper
├── Configuration/
│   ├── FlexForms/           # Page.xml, RouteReference.xml
│   ├── TCA/                 # Route TCA and page overrides
│   └── TypoScript/          # Constants and setup
├── Resources/
│   ├── Private/             # Templates (Page, RouteReference), Language Files
│   └── Public/              # CSS, JavaScript, Icons
├── composer.json            # Composer configuration
├── ext_emconf.php          # Extension configuration
├── ext_localconf.php       # Plugin registration
├── ext_tables.php          # TCA registration
└── ext_tables.sql          # Database schema
```

## Requirements

- TYPO3 12.4 LTS or higher
- PHP 8.1 or higher (as per TYPO3 12.4 requirements)

## Support

For issues, feature requests, or contributions:
- Create an issue in the project repository
- Check the language files for field descriptions
- Review the TCA configuration for available options

## License

This extension is licensed under GPL-2.0-or-later.

## Credits

Developed following TYPO3 best practices and coding standards.
