# Automatic Slug Updates

The Flight Landing Pages extension now supports automatic updates of URL segments (slugs) when parent page slugs are changed. This ensures that all flight route URLs remain consistent and accessible when the page structure changes.

## How It Works

### DataHandler Hook
The extension uses a TYPO3 DataHandler hook (`DataHandlerHook`) that automatically detects when a page slug is modified and updates all related child slugs accordingly.

### Components

1. **DataHandlerHook** (`Classes/Hooks/DataHandlerHook.php`)
   - Listens for page slug changes via `processDatamap_afterDatabaseOperations`
   - Triggers automatic updates when a parent page slug changes

2. **SlugUpdateService** (`Classes/Service/SlugUpdateService.php`)
   - Contains the core logic for updating child slugs
   - <PERSON>les both landing pages (doktype 201) and flight routes
   - Provides statistics about updates performed

## What Gets Updated

### Landing Pages (doktype 201)
When a parent page slug changes, all child landing pages that have slugs starting with the old parent slug will be automatically updated to use the new parent slug.

**Example:**
- Parent page slug changes from `/flights` to `/travel/flights`
- Child landing page slug `/flights/europe` becomes `/travel/flights/europe`

### Flight Routes
Flight routes are updated in two scenarios:

1. **When a landing page slug changes**: All flight routes under that landing page get their slugs regenerated with the new parent path
2. **When a parent page slug changes**: All flight routes under affected child landing pages get updated

**Examples:**
- Landing page slug changes from `/flights/europe` to `/flights/european-destinations`
- Flight route slug `/flights/europe/ber-sof` becomes `/flights/european-destinations/ber-sof`

- Parent page slug changes from `/flights` to `/travel/flights`
- Landing page slug `/flights/europe` becomes `/travel/flights/europe`
- Flight route slug `/flights/europe/ber-sof` becomes `/travel/flights/europe/ber-sof`

## Automatic Triggering

The automatic slug updates are triggered when:

1. A page record is updated (not created)
2. The `slug` field is modified
3. The slug value actually changes (not just saved with the same value)

## Manual Commands

### Update Child Slugs Command
For testing or manual corrections, you can use the console command:

```bash
ddev exec vendor/bin/typo3 flight:update-child-slugs <pageId> [oldSlug] [newSlug] [--routes-only]
```

**Parameters:**
- `pageId` (required): The page ID whose children should be updated
- `oldSlug` (optional): The old slug value (defaults to current slug)
- `newSlug` (optional): The new slug value (defaults to current slug for regeneration)
- `--routes-only` (optional): Update only flight routes for this landing page

**Examples:**
```bash
# Update all child slugs for page ID 123 (regenerate with current slug)
ddev exec vendor/bin/typo3 flight:update-child-slugs 123

# Simulate a slug change from /flights to /travel/flights for page ID 123
ddev exec vendor/bin/typo3 flight:update-child-slugs 123 /flights /travel/flights

# Update only flight routes for landing page ID 5 (useful after manual slug changes)
ddev exec vendor/bin/typo3 flight:update-child-slugs 5 --routes-only
```

### Legacy Update Slugs Command
The existing command for updating flight route slugs is still available:

```bash
ddev exec vendor/bin/typo3 flight:update-slugs
```

This command updates all existing flight route records to ensure they follow the correct slug format.

## Technical Details

### Slug Generation Logic
The slug update service uses the same logic as the `SlugModifier` class to ensure consistency:

1. **Type-aware slug generation**: Different slug formats for airports vs cities/countries
2. **Parent path inclusion**: Slugs include the full parent page path
3. **Sanitization**: Proper handling of special characters and spaces

### Performance Considerations
- Updates are performed efficiently using direct database queries
- Only records that actually need updating are modified
- Recursive page tree traversal is optimized to avoid unnecessary queries

### Error Handling
- The service provides detailed statistics about updates performed
- Errors are captured and can be logged or displayed
- Failed updates don't prevent other updates from completing

## Configuration

The automatic slug update feature is enabled by default when the extension is installed. No additional configuration is required.

### Disabling Automatic Updates
If you need to disable automatic slug updates, you can remove or comment out the DataHandler hook registration in `ext_localconf.php`:

```php
// Comment out this line to disable automatic slug updates
// $GLOBALS['TYPO3_CONF_VARS']['SC_OPTIONS']['t3lib/class.t3lib_tcemain.php']['processDatamapClass'][] = 
//     \Bgs\FlightLandingPages\Hooks\DataHandlerHook::class;
```

## Troubleshooting

### Slugs Not Updating
1. Check that the page slug actually changed (not just saved)
2. Verify that child pages are landing pages (doktype 201)
3. Clear TYPO3 caches: `ddev exec vendor/bin/typo3 cache:flush`

### Testing Updates
Use the manual command to test slug updates:
```bash
ddev exec vendor/bin/typo3 flight:update-child-slugs <pageId>
```

### Checking Update Results
The command provides detailed output showing:
- Number of landing pages updated
- Number of flight routes updated
- Any errors encountered

## Integration with Existing Features

The automatic slug update feature integrates seamlessly with:
- Virtual route detection and handling
- URL generation services
- Sitemap generation
- Backend preview functionality

All existing functionality continues to work as expected with the updated slugs.
