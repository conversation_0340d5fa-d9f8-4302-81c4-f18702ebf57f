<?php
defined('TYPO3') or die();

call_user_func(static function () {
    // Register backend module if needed
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addLLrefForTCAdescr(
        'tx_landingpages_domain_model_flightroute',
        'EXT:landing-pages/Resources/Private/Language/locallang_csh_tx_landingpages_domain_model_flightroute.xlf'
    );
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::allowTableOnStandardPages('tx_landingpages_domain_model_flightroute');

    // Allow Template Mappings table on standard pages (needed for inline records)
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::allowTableOnStandardPages('tx_landingpages_domain_model_templatemapping');
});
