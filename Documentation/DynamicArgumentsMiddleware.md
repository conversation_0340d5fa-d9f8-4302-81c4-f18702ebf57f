# Dynamic Arguments Middleware

This document explains how to use the DynamicArgumentsMiddleware to add dynamic arguments to the TYPO3 routing system.

## Overview

The DynamicArgumentsMiddleware allows you to add custom dynamic arguments to the routing before the TypoScriptFrontendInitialization middleware processes the request. This is useful when you need to add parameters that should be considered for caching or when you need to pass additional data to the page rendering process.

## How It Works

The middleware runs after the PageArgumentValidator middleware but before the TypoScriptFrontendInitialization middleware. This allows it to manipulate dynamic arguments and cHash after routing has been processed but before the page is rendered. It retrieves the PageArguments object from the request, adds custom dynamic arguments, and then creates a new PageArguments object with the updated arguments.

The middleware is registered in the `Configuration/RequestMiddlewares.php` file with the following configuration:

```php
'landing-pages/dynamic-arguments' => [
    'target' => \Bgs\LandingPages\Middleware\DynamicArgumentsMiddleware::class,
    'after' => [
        'typo3/cms-frontend/page-argument-validator',
    ],
    'before' => [
        'typo3/cms-frontend/tsfe',
    ],
],
```

It's also registered in `ext_localconf.php` to ensure it's loaded properly:

```php
$GLOBALS['TYPO3_CONF_VARS']['HTTP']['middleware']['frontend']['landing-pages/dynamic-arguments'] = [
    'target' => \Bgs\LandingPages\Middleware\DynamicArgumentsMiddleware::class,
    'after' => [
        'typo3/cms-frontend/page-argument-validator',
    ],
    'before' => [
        'typo3/cms-frontend/tsfe',
    ],
];
```

## Customizing Dynamic Arguments

To add your own dynamic arguments, you can extend the `getDynamicArguments` method in the DynamicArgumentsMiddleware class:

```php
protected function getDynamicArguments(ServerRequestInterface $request, PageArguments $pageArguments): array
{
    $dynamicArguments = [];

    // Add your custom dynamic arguments here
    $dynamicArguments['custom_param'] = 'custom_value';

    // You can also add dynamic arguments based on request attributes
    $someAttribute = $request->getAttribute('some_attribute');
    if ($someAttribute) {
        $dynamicArguments['attribute_data'] = $someAttribute;
    }

    return $dynamicArguments;
}
```

## Use Cases

Here are some common use cases for adding dynamic arguments to the routing:

1. **User-specific content**: Add user information as dynamic arguments to generate user-specific content while still using caching.
2. **Session data**: Include session data in the routing to create session-aware cached pages.
3. **Virtual routes**: Add information about virtual routes to the routing to ensure proper caching and processing.
4. **A/B testing**: Include A/B testing parameters in the routing to serve different versions of the same page.

## Example: Adding Route Data and Handling cHash

The default implementation adds route information as dynamic arguments if a virtual route context is available and handles cHash manipulation:

```php
protected function getDynamicArguments(ServerRequestInterface $request, PageArguments $pageArguments): array
{
    // Initialize dynamic arguments array
    $dynamicArguments = [];

    // Get virtual route context if available
    $virtualRouteContext = $request->getAttribute('landing-pages.virtual_route_context');
    if ($virtualRouteContext) {
        // Add route information as dynamic arguments
        $dynamicArguments['route_data'] = [
            'route_slug' => $virtualRouteContext->getRouteSlug(),
            'original_path' => $virtualRouteContext->getOriginalPath(),
        ];

        // Add a unique identifier for this virtual route to ensure proper caching
        $dynamicArguments['virtual_route_id'] = md5($virtualRouteContext->getRouteSlug());
    }

    // Handle cHash manipulation if needed
    // Since we're running after PageArgumentValidator, we can safely manipulate the cHash
    // without triggering validation errors
    $arguments = $pageArguments->getArguments();
    if (isset($arguments['cHash'])) {
        // We can manipulate the cHash here if needed
        // For example, we could regenerate it based on the new dynamic arguments
        // or we could remove it to force a non-cached version

        // For now, we'll just keep the existing cHash
        $dynamicArguments['cHash'] = $arguments['cHash'];
    }

    return $dynamicArguments;
}
```

## Events as an Alternative

If you prefer using events instead of middlewares, you can use the following events to modify the routing:

1. `AfterPageAndLanguageIsResolvedEvent`: This event is dispatched after the page and language have been resolved.
2. `AfterPageWithRootLineIsResolvedEvent`: This event is dispatched after the page with rootline has been resolved.

These events can be used to modify the PageArguments object before it's processed by TypoScriptFrontendInitialization.

## Conclusion

The DynamicArgumentsMiddleware provides a flexible way to add custom dynamic arguments to the TYPO3 routing system. By customizing the `getDynamicArguments` method, you can add any parameters you need to the routing process.
