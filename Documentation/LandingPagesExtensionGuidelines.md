# Landing Pages Extension Guidelines

## Table of Contents
1. [Introduction](#introduction)
2. [DDEV Environment Setup](#ddev-environment-setup)
3. [TYPO3 v12.4 Compatibility](#typo3-v124-compatibility)
4. [Extension Architecture](#extension-architecture)
5. [Virtual Routes](#virtual-routes)
6. [Middlewares](#middlewares)
7. [Template System](#template-system)
8. [Configuration](#configuration)
9. [Development Workflow](#development-workflow)
10. [Troubleshooting](#troubleshooting)

## Introduction

The Landing Pages extension provides a flexible system for creating and managing dynamic landing pages in TYPO3. It allows for the creation of virtual routes that map to landing pages, making it possible to have SEO-friendly URLs for dynamically generated content.

## DDEV Environment Setup

### Prerequisites
- Docker
- DDEV installed on your system

### Setup Steps
1. Clone the repository
2. Navigate to the project directory
3. Start DDEV:
   ```bash
   ddev start
   ```
4. Import the database:
   ```bash
   ddev import-db --src=path/to/database.sql
   ```
5. Access the TYPO3 backend:
   ```
   https://typo.fie.ddev.site/typo3
   ```

### DDEV Commands
- `ddev typo3cms` - Run TYPO3 CLI commands
- `ddev composer` - Run Composer commands
- `ddev exec` - Execute commands in the container

## TYPO3 v12.4 Compatibility

The Landing Pages extension is fully compatible with TYPO3 v12.4 and takes advantage of its features:

### Key TYPO3 v12.4 Features Used
- PSR-14 Event System for hooks and signals
- Dependency Injection via Services.yaml
- Middleware API for request processing
- Symfony Console Commands for CLI operations
- Site Configuration API for multi-site handling

### Compatibility Notes
- The extension uses conditional code for backward compatibility with TYPO3 v11 where necessary
- Page TSconfig is loaded automatically in v12+ from Configuration/page.tsconfig

## Extension Architecture

The Landing Pages extension follows a modular architecture with clear separation of concerns:

### Core Components
- **Domain Models**: Define the data structure for landing pages, flight routes, and template mappings
- **Repositories**: Handle database operations for the models
- **Services**: Provide business logic for virtual routes, templates, and URL generation
- **Middlewares**: Process HTTP requests for virtual routes
- **Event Listeners**: React to TYPO3 events for virtual page handling
- **Controllers**: Handle frontend plugin rendering and backend operations

## Virtual Routes

Virtual routes are a key feature of the Landing Pages extension, allowing dynamic content to be served at SEO-friendly URLs.

### How Virtual Routes Work
1. A request comes in with a path that doesn't match a physical page
2. The VirtualRouteHandler middleware checks if the path matches a virtual route pattern
3. If matched, the VirtualRouteService creates a virtual page based on a template page
4. The content is processed with placeholders replaced by dynamic values
5. The response is sent to the browser as if it were a physical page

### Virtual Route Configuration
Virtual routes are configured through the landing page records and flight route records in the TYPO3 backend.

### URL Structure
Virtual routes follow a pattern defined in the landing page record, with placeholders for dynamic values.

## Middlewares

The extension uses TYPO3's middleware system to process requests:

### VirtualRouteHandler
- Runs before the PageResolver middleware
- Detects if a request matches a virtual route
- Sets up the virtual route context for later processing

### DynamicArgumentsMiddleware
- Runs after PageArgumentValidator but before TypoScriptFrontendInitialization
- Adds dynamic arguments to the routing
- Manipulates the cHash to ensure proper caching

### Middleware Configuration
Middlewares are configured in both ext_localconf.php and Services.yaml to ensure proper loading order.

## Template System

The extension uses TYPO3's Fluid template system for rendering landing pages:

### Template Types
- **Landing Page**: General template for landing pages
- **Flight Landing Page**: Specialized template for flight routes

### Template Resolution
The TemplateResolutionService determines which template to use based on the landing page type and template mapping records.

### Placeholder Processing
The VirtualRouteService processes placeholders in the content, replacing them with dynamic values from the flight route record.

## Configuration

### Extension Configuration
- **TypoScript**: Configuration for frontend rendering
- **TCA**: Backend form configuration
- **Services.yaml**: Dependency injection configuration
- **ext_localconf.php**: Extension initialization

### Site Configuration
The extension integrates with TYPO3's site configuration system for multi-site support.

## Development Workflow

### Adding New Landing Page Types
1. Create a new template file
2. Add a template mapping record
3. Configure the landing page record to use the new template

### Creating Virtual Routes
1. Create a landing page record with a route pattern
2. Create flight route records with specific values
3. Test the virtual routes by accessing them in the browser

### Updating Slugs
The extension provides commands for updating slugs:
```bash
ddev typo3cms landing:update-slugs
ddev typo3cms landing:update-child-slugs
```

## Troubleshooting

### Common Issues
- **404 Errors**: Check if the virtual route pattern matches the requested URL
- **Placeholder Not Replaced**: Verify that the placeholder name matches the field name in the flight route record
- **Caching Issues**: Clear the TYPO3 cache after making changes to virtual routes

### Debugging
- Enable TYPO3 debug mode in LocalConfiguration.php
- Check the TYPO3 system log for errors
- Use the TYPO3 Debug Console extension for more detailed debugging

### Support
For additional support, refer to the extension documentation or contact the development team.
