<?php

declare(strict_types=1);

namespace Bgs\LandingPages\EventListener;

use Bgs\LandingPages\Domain\Model\VirtualRouteContext;
use Bgs\LandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Frontend\Event\AfterPageWithRootLineIsResolvedEvent;

/**
 * Virtual Page Replacement Listener
 * 
 * Listens to AfterPageWithRootLineIsResolvedEvent to replace the resolved page
 * with virtual page data when processing virtual routes.
 */
class VirtualPageReplacementListener
{
    /**
     * @var VirtualRouteService
     */
    private $virtualRouteService;

    public function __construct(VirtualRouteService $virtualRouteService)
    {
        $this->virtualRouteService = $virtualRouteService;
    }

    public function __invoke(AfterPageWithRootLineIsResolvedEvent $event): void
    {
        // Get virtual route data directly from the request attribute (set by middleware)
        $request = $event->getRequest();
        $virtualRouteContext = $request->getAttribute('flight_landing_pages.virtual_route_context');

        if (!$virtualRouteContext instanceof VirtualRouteContext || !$virtualRouteContext->isVirtualRoute()) {
            return;
        }

        // Get the controller and access page data directly
        $controller = $event->getController();
        $currentPage = $controller->page; // This is now the template page (due to setPageId)
        $currentRootLine = $controller->rootLine;

        // Get landing page data from virtual route context
        $landingPageData = $virtualRouteContext->getLandingPageData();
        $routeData = $virtualRouteContext->getFlightRouteData();

        // Create a hybrid page: landing page structure + template page content
        // Start with template page (for TypoScript resolution) but use landing page properties
        $modifiedPage = $currentPage; // Start with template page

        // Replace structural properties with landing page data
        $structuralFields = [
            'uid',      // Use landing page UID for routing
            'pid',      // Use landing page PID for hierarchy
            'slug',     // Use landing page slug for routing
            'sorting',  // Use landing page sorting
            'crdate',   // Use landing page creation date
            // Keep other structural fields from landing page
            'deleted',
            'hidden',
            'starttime',
            'endtime',
            'fe_group',
            'sys_language_uid',
            'l10n_parent',
            'l10n_source',
        ];

        // Update the rootline to include our modified page
        $modifiedRootLine = $currentRootLine;
        if (!empty($modifiedRootLine)) {
            // Replace the last entry (current page) with our modified page
            $modifiedRootLine[array_key_first($modifiedRootLine)] = $modifiedPage;
        }

        // Set the modified page and rootline directly on the controller
        $controller->page = $modifiedPage;
        $controller->rootLine = $modifiedRootLine;
    }
}
