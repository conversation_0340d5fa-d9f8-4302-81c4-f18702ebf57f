<?php

declare(strict_types=1);

namespace Bgs\LandingPages\EventListener;

use Bgs\LandingPages\Domain\Model\VirtualRouteContext;
use Bgs\LandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Frontend\Event\BeforePageIsResolvedEvent;

/**
 * Virtual Route Detection Listener
 * 
 * Listens to BeforePageIsResolvedEvent to detect virtual routes
 * and store the context for later processing.
 */
class VirtualRouteDetectionListener
{
    /**
     * @var VirtualRouteService
     */
    private $virtualRouteService;

    public function __construct(VirtualRouteService $virtualRouteService)
    {
        $this->virtualRouteService = $virtualRouteService;
    }

    public function __invoke(BeforePageIsResolvedEvent $event): void
    {
        $request = $event->getRequest();

        // Check if this is a virtual route request (set by our middleware)
        $virtualRouteContext = $request->getAttribute('landing-pages.virtual_route_context');

        if ($virtualRouteContext instanceof VirtualRouteContext && $virtualRouteContext->isVirtualRoute()) {
            // Store virtual route context for later processing by other event listeners
            // Convert to legacy format for backward compatibility with existing service
            $this->virtualRouteService->setVirtualRoute($virtualRouteContext->toVirtualRouteMatch());
        } else {
            // Clear any previous virtual route state for normal requests
            $this->virtualRouteService->clearVirtualRoute();
        }
    }
}
