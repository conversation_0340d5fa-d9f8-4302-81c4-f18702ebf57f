<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Hooks;

use Bgs\LandingPages\Service\SlugUpdateService;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\DataHandling\DataHandler;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * DataHandler hook for automatic slug updates
 *
 * Handles automatic updates of flight route and landing page slugs
 * when parent page slugs are changed.
 */
class DataHandlerHook
{
    protected SlugUpdateService $slugUpdateService;

    /**
     * Store old slugs before they are updated
     * @var array
     */
    protected array $oldSlugs = [];

    public function __construct()
    {
        $this->slugUpdateService = GeneralUtility::makeInstance(SlugUpdateService::class);
    }

    /**
     * Hook that is called before field processing to capture old slugs
     *
     * @param array $incomingFieldArray Field values being processed
     * @param string $table Table name
     * @param string|int $id Record ID
     * @param DataHandler $dataHandler DataHandler instance
     */
    public function processDatamap_preProcessFieldArray(
        array &$incomingFieldArray,
        string $table,
        $id,
        DataHandler $dataHandler
    ): void {
        // Only process page updates where slug is being changed
        if ($table !== 'pages' || !isset($incomingFieldArray['slug'])) {
            return;
        }

        $pageId = (int)$id;
        if ($pageId <= 0) {
            return;
        }

        // Store the old slug before it gets updated
        $oldSlug = $this->getPageSlug($pageId);
        if (!empty($oldSlug)) {
            $this->oldSlugs[$pageId] = $oldSlug;
        }
    }

    /**
     * Hook that is called after database operations
     *
     * @param string $status Operation status (new, update)
     * @param string $table Table name
     * @param string|int $id Record ID
     * @param array $fieldArray Field values
     * @param DataHandler $dataHandler DataHandler instance
     */
    public function processDatamap_afterDatabaseOperations(
        string $status,
        string $table,
        $id,
        array $fieldArray,
        DataHandler $dataHandler
    ): void {
        // Only process page updates where slug has changed
        if ($table !== 'pages' || $status !== 'update' || !isset($fieldArray['slug'])) {
            return;
        }

        $pageId = (int)$id;
        if ($pageId <= 0) {
            return;
        }

        // Get the old and new slugs
        $oldSlug = $this->oldSlugs[$pageId] ?? '';
        $newSlug = $fieldArray['slug'];

        // Only proceed if slug actually changed
        if (empty($oldSlug) || $oldSlug === $newSlug) {
            // Clean up stored slug
            unset($this->oldSlugs[$pageId]);
            return;
        }

        // Check if this is a landing page (doktype 201) - if so, update its flight routes
        $pageInfo = $this->getPageInfo($pageId);
        if ($pageInfo && (int)$pageInfo['doktype'] === 201) {
            // This is a landing page - update its flight routes directly
            $routesUpdated = $this->slugUpdateService->updateFlightRouteSlugs($pageId);
        }

        // Update child landing pages and their flight routes (for any page type)
        $stats = $this->slugUpdateService->updateChildSlugs($pageId, $oldSlug, $newSlug);

        // Clean up stored slug
        unset($this->oldSlugs[$pageId]);

        // Log results if needed (could be extended with proper logging)
        if (!empty($stats['errors'])) {
            // Handle errors if needed
        }
    }

    /**
     * Get current slug for a page
     *
     * @param int $pageId Page ID
     * @return string Current slug
     */
    protected function getPageSlug(int $pageId): string
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $result = $queryBuilder
            ->select('slug')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative();

        return $result['slug'] ?? '';
    }

    /**
     * Get page information including doktype
     *
     * @param int $pageId Page ID
     * @return array|null Page information or null if not found
     */
    protected function getPageInfo(int $pageId): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $result = $queryBuilder
            ->select('uid', 'pid', 'title', 'slug', 'doktype')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative();

        return $result ?: null;
    }
}
