<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Middleware;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\Routing\PageArguments;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * This middleware adds dynamic arguments to the routing
 * It runs after PageArgumentValidator but before TypoScriptFrontendInitialization
 * This allows us to manipulate the dynamic arguments and cHash after routing has been processed
 */

class DynamicArgumentsMiddleware implements MiddlewareInterface
{
    /**
     * Process the request by adding dynamic arguments to the routing
     */
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        /** @var PageArguments|null $pageArguments */
        $pageArguments = $request->getAttribute('routing');

        if ($pageArguments instanceof PageArguments) {
            // Get the current arguments
            $arguments = $pageArguments->getArguments();

            $virtualRouteContext = $request->getAttribute('landing-pages.virtual_route_context');

            $arguments['lp_route'] = $virtualRouteContext->getOriginalPath();
            $arguments['cHash'] =  sha1($virtualRouteContext->getOriginalPath());

            $newPageArguments = GeneralUtility::makeInstance(
                PageArguments::class,
                $pageArguments->getPageId(),
                $pageArguments->getPageType(),
                $pageArguments->getRouteArguments(),
                $pageArguments->getStaticArguments(),
                array_merge($pageArguments->getDynamicArguments(), $arguments)
            );

            $request = $request->withAttribute('routing', $newPageArguments);
            return $handler->handle($request);
        }

        return $handler->handle($request);
    }
}
