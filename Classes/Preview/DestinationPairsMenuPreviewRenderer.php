<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Preview;

use TYPO3\CMS\Backend\Preview\StandardContentPreviewRenderer;
use TYPO3\CMS\Backend\View\BackendLayout\Grid\GridColumnItem;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Preview renderer for Destination Pairs Menu content element
 */
class DestinationPairsMenuPreviewRenderer extends StandardContentPreviewRenderer
{
    /**
     * Render the preview for the Destination Pairs Menu content element
     */
    public function renderPageModulePreviewContent(GridColumnItem $item): string
    {
        $record = $item->getRecord();
        $content = '';

        // Add header if present
        if (!empty($record['header'])) {
            $content .= '<h4>' . htmlspecialchars($record['header']) . '</h4>';
        }

        // Add description
        $content .= '<p><strong>Destination Pairs Menu</strong></p>';
        $content .= '<p>This element will display a dynamic menu of available flight destinations.</p>';

        // Get page information to show context
        $pageUid = (int)$record['pid'];
        $pageInfo = $this->getPageInfo($pageUid);
        
        if ($pageInfo) {
            if ((int)$pageInfo['doktype'] === 201) {
                $content .= '<p style="color: green;">✓ Available on Flight Landing Page</p>';
                
                // Show number of available routes
                $routeCount = $this->getRouteCount($pageUid);
                if ($routeCount > 0) {
                    $content .= '<p>Available destinations: <strong>' . $routeCount . '</strong></p>';
                } else {
                    $content .= '<p style="color: orange;">⚠ No destination pairs configured for this page</p>';
                }
            } else {
                $content .= '<p style="color: red;">⚠ This content element only works on Flight Landing Pages (doktype 201)</p>';
            }
        }

        return $content;
    }

    /**
     * Get page information
     */
    protected function getPageInfo(int $pageUid): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        return $queryBuilder
            ->select('uid', 'title', 'doktype')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', 0)
            )
            ->executeQuery()
            ->fetchAssociative() ?: null;
    }

    /**
     * Get count of flight routes for this page
     */
    protected function getRouteCount(int $pageUid): int
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_landingpages_domain_model_flightroute');

        return (int)$queryBuilder
            ->count('uid')
            ->from('tx_landingpages_domain_model_flightroute')
            ->where(
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($pageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', 0),
                $queryBuilder->expr()->eq('is_active', 1)
            )
            ->executeQuery()
            ->fetchOne();
    }
}
