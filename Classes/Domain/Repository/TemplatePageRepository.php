<?php
namespace Bgs\LandingPages\Domain\Repository;

use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Persistence\Repository;
use Bgs\LandingPages\Domain\Model\TemplatePage;

/**
 * Repository for Template Pages (doktype=200)
 */
class TemplatePageRepository extends Repository
{
    /**
     * Find all accessible template pages
     */
    public function findAccessible(): array
    {
        $query = $this->createQuery();
        $query->matching(
            $query->logicalAnd(
                $query->equals('doktype', 200),
                $query->equals('hidden', false)
            )
        );
        $query->setOrderings(['title' => 'ASC']);
        return $query->execute()->toArray();
    }

    /**
     * Find template page by UID with content elements
     */
    public function findByUidWithContent(int $uid): ?array
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);

        // Get page data
        $pageConnection = $connectionPool->getConnectionForTable('pages');
        $page = $pageConnection->select(
            ['*'],
            'pages',
            [
                'uid' => $uid,
                'doktype' => 200,
                'deleted' => 0
            ]
        )->fetchAssociative();

        if (!$page) {
            return null;
        }

        // Get content elements
        $contentConnection = $connectionPool->getConnectionForTable('tt_content');
        $contentElements = $contentConnection->select(
            ['*'],
            'tt_content',
            [
                'pid' => $uid,
                'deleted' => 0,
                'hidden' => 0
            ],
            [],
            ['sorting' => 'ASC']
        )->fetchAllAssociative();

        $page['content_elements'] = $contentElements;

        return $page;
    }

    /**
     * Find template pages by storage folder
     */
    public function findByStorageFolder(int $storagePid): array
    {
        $query = $this->createQuery();
        $query->matching(
            $query->logicalAnd(
                $query->equals('pid', $storagePid),
                $query->equals('doktype', 200),
                $query->equals('hidden', false)
            )
        );
        $query->setOrderings(['title' => 'ASC']);
        return $query->execute()->toArray();
    }

    /**
     * Get content elements for a template page
     */
    public function getContentElements(int $templatePageUid): array
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
        $connection = $connectionPool->getConnectionForTable('tt_content');

        return $connection->select(
            ['*'],
            'tt_content',
            [
                'pid' => $templatePageUid,
                'deleted' => 0,
                'hidden' => 0
            ],
            [],
            ['sorting' => 'ASC', 'colPos' => 'ASC']
        )->fetchAllAssociative();
    }

    /**
     * Check if template page exists and is accessible
     */
    public function isAccessible(int $uid): bool
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
        $connection = $connectionPool->getConnectionForTable('pages');

        $page = $connection->select(
            ['uid', 'hidden', 'starttime', 'endtime'],
            'pages',
            [
                'uid' => $uid,
                'doktype' => 200,
                'deleted' => 0
            ]
        )->fetchAssociative();

        if (!$page) {
            return false;
        }

        if ($page['hidden']) {
            return false;
        }

        $now = time();

        if ($page['starttime'] > 0 && $now < $page['starttime']) {
            return false;
        }

        if ($page['endtime'] > 0 && $now > $page['endtime']) {
            return false;
        }

        return true;
    }

    /**
     * Get template page statistics
     */
    public function getStatistics(int $uid): array
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);

        // Count content elements
        $contentConnection = $connectionPool->getConnectionForTable('tt_content');
        $contentCount = $contentConnection->count(
            '*',
            'tt_content',
            [
                'pid' => $uid,
                'deleted' => 0
            ]
        );

        // Count landing pages using this template
        $pagesConnection = $connectionPool->getConnectionForTable('pages');
        $landingPageCount = $pagesConnection->count(
            '*',
            'pages',
            [
                'tx_landingpages_template_page' => $uid,
                'doktype' => 201,
                'deleted' => 0
            ]
        );

        return [
            'content_elements' => $contentCount,
            'landing_pages' => $landingPageCount
        ];
    }
}
