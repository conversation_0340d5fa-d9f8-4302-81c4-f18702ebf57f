<?php
namespace Bgs\LandingPages\Domain\Repository;

use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Persistence\Repository;
use Bgs\LandingPages\Domain\Model\LandingPage;

/**
 * Repository for Landing Pages (doktype=201)
 */
class LandingPageRepository extends Repository
{
    /**
     * Find all accessible landing pages
     */
    public function findAccessible(): array
    {
        $query = $this->createQuery();
        $query->matching(
            $query->logicalAnd(
                $query->equals('doktype', 201),
                $query->equals('hidden', false)
            )
        );
        $query->setOrderings(['title' => 'ASC']);
        return $query->execute()->toArray();
    }

    /**
     * Find landing pages for a specific site
     */
    public function findBySite(string $siteIdentifier): array
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
        $connection = $connectionPool->getConnectionForTable('pages');

        // Find site root page
        $siteRoot = $connection->select(
            ['uid'],
            'pages',
            ['is_siteroot' => 1, 'deleted' => 0]
        )->fetchAssociative();

        if (!$siteRoot) {
            return [];
        }

        // Find landing pages under this site
        $landingPages = $connection->select(
            ['*'],
            'pages',
            [
                'doktype' => 201,
                'deleted' => 0,
                'hidden' => 0
            ]
        )->fetchAllAssociative();

        // Filter by site tree (simplified - in real implementation would use proper tree traversal)
        return array_filter($landingPages, function($page) use ($siteRoot) {
            return $this->isPageInSiteTree($page['uid'], $siteRoot['uid']);
        });
    }

    /**
     * Find landing page by URL pattern match
     */
    public function findByUrlMatch(string $path, string $siteIdentifier): ?array
    {
        $landingPages = $this->findBySite($siteIdentifier);

        foreach ($landingPages as $landingPage) {
            if ($this->matchesUrlPattern($path, $landingPage['tx_landingpages_url_pattern'])) {
                return $landingPage;
            }
        }

        return null;
    }

    /**
     * Get landing page with template page data
     */
    public function findByUidWithTemplate(int $uid): ?array
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
        $connection = $connectionPool->getConnectionForTable('pages');

        $landingPage = $connection->select(
            ['*'],
            'pages',
            [
                'uid' => $uid,
                'doktype' => 201,
                'deleted' => 0
            ]
        )->fetchAssociative();

        if (!$landingPage) {
            return null;
        }

        // Get template page data if referenced
        if ($landingPage['tx_landingpages_template_page']) {
            $templatePage = $connection->select(
                ['*'],
                'pages',
                [
                    'uid' => $landingPage['tx_landingpages_template_page'],
                    'doktype' => 200,
                    'deleted' => 0
                ]
            )->fetchAssociative();

            $landingPage['template_page_data'] = $templatePage;
        }

        return $landingPage;
    }

    /**
     * Find landing pages using a specific template
     */
    public function findByTemplate(int $templatePageUid): array
    {
        $query = $this->createQuery();
        $query->matching(
            $query->logicalAnd(
                $query->equals('doktype', 201),
                $query->equals('templatePage', $templatePageUid),
                $query->equals('hidden', false)
            )
        );
        $query->setOrderings(['title' => 'ASC']);
        return $query->execute()->toArray();
    }

    /**
     * Check if URL pattern matches path
     */
    protected function matchesUrlPattern(string $path, string $urlPattern): bool
    {
        if (empty($urlPattern)) {
            return false;
        }

        // Convert URL pattern to regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $urlPattern);
        $pattern = '#^' . $pattern . '$#';

        return preg_match($pattern, $path) === 1;
    }

    /**
     * Extract parameters from URL using pattern
     */
    public function extractUrlParameters(string $path, string $urlPattern): ?array
    {
        if (empty($urlPattern)) {
            return null;
        }

        // Get placeholder names
        preg_match_all('/\{([^}]+)\}/', $urlPattern, $placeholderMatches);
        $placeholders = $placeholderMatches[1] ?? [];

        // Convert pattern to regex and extract values
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $urlPattern);
        $pattern = '#^' . $pattern . '$#';

        if (preg_match($pattern, $path, $matches)) {
            array_shift($matches); // Remove full match
            return array_combine($placeholders, $matches);
        }

        return null;
    }

    /**
     * Check if page is in site tree (simplified implementation)
     */
    protected function isPageInSiteTree(int $pageUid, int $siteRootUid): bool
    {
        // Simplified implementation - in production would use proper tree traversal
        // For now, just check if page exists and is not deleted
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
        $connection = $connectionPool->getConnectionForTable('pages');

        $page = $connection->select(
            ['uid'],
            'pages',
            [
                'uid' => $pageUid,
                'deleted' => 0
            ]
        )->fetchAssociative();

        return $page !== false;
    }

    /**
     * Get statistics for landing page
     */
    public function getStatistics(int $uid): array
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);

        // Count flight routes using this landing page (using parent page ID)
        $routeConnection = $connectionPool->getConnectionForTable('tx_landingpages_domain_model_flightroute');
        $routeCount = $routeConnection->count(
            '*',
            'tx_landingpages_domain_model_flightroute',
            [
                'pid' => $uid,
                'deleted' => 0,
                'is_active' => 1
            ]
        );

        return [
            'flight_routes' => $routeCount
        ];
    }
}
