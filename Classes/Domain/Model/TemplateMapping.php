<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Domain\Model;

use TYPO3\CMS\Extbase\DomainObject\AbstractEntity;

/**
 * Template mapping for route type combinations
 */
class TemplateMapping extends AbstractEntity
{
    protected int $landingPageUid = 0;
    protected string $originType = '';
    protected string $destinationType = '';
    protected int $templatePageUid = 0;

    public function getLandingPageUid(): int
    {
        return $this->landingPageUid;
    }

    public function setLandingPageUid(int $landingPageUid): void
    {
        $this->landingPageUid = $landingPageUid;
    }

    public function getOriginType(): string
    {
        return $this->originType;
    }

    public function setOriginType(string $originType): void
    {
        $this->originType = $originType;
    }

    public function getDestinationType(): string
    {
        return $this->destinationType;
    }

    public function setDestinationType(string $destinationType): void
    {
        $this->destinationType = $destinationType;
    }

    public function getTemplatePageUid(): int
    {
        return $this->templatePageUid;
    }

    public function setTemplatePageUid(int $templatePageUid): void
    {
        $this->templatePageUid = $templatePageUid;
    }

    /**
     * Get type combination as string for display
     */
    public function getTypeCombination(): string
    {
        return $this->originType . ' → ' . $this->destinationType;
    }
}
