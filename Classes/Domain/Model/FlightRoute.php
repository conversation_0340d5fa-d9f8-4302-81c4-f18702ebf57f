<?php
namespace Bgs\LandingPages\Domain\Model;

use TYPO3\CMS\Extbase\DomainObject\AbstractEntity;

class FlightRoute extends AbstractEntity
{
    protected string $originCode = '';
    protected string $originName = '';
    protected string $originType = 'airport';
    protected string $destinationCode = '';
    protected string $destinationName = '';
    protected string $destinationType = 'airport';
    protected string $routeSlug = '';
    protected bool $isActive = true;

    // Getters and setters
    public function getOriginCode(): string
    {
        return $this->originCode;
    }

    public function setOriginCode(string $originCode): void
    {
        $this->originCode = $originCode;
    }

    public function getOriginName(): string
    {
        return $this->originName;
    }

    public function setOriginName(string $originName): void
    {
        $this->originName = $originName;
    }

    public function getOriginType(): string
    {
        return $this->originType;
    }

    public function setOriginType(string $originType): void
    {
        $this->originType = $originType;
    }

    public function getDestinationCode(): string
    {
        return $this->destinationCode;
    }

    public function setDestinationCode(string $destinationCode): void
    {
        $this->destinationCode = $destinationCode;
    }

    public function getDestinationName(): string
    {
        return $this->destinationName;
    }

    public function setDestinationName(string $destinationName): void
    {
        $this->destinationName = $destinationName;
    }

    public function getDestinationType(): string
    {
        return $this->destinationType;
    }

    public function setDestinationType(string $destinationType): void
    {
        $this->destinationType = $destinationType;
    }

    public function getRouteSlug(): string
    {
        return $this->routeSlug;
    }

    public function setRouteSlug(string $routeSlug): void
    {
        $this->routeSlug = $routeSlug;
    }

    public function getIsActive(): bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): void
    {
        $this->isActive = $isActive;
    }
}
