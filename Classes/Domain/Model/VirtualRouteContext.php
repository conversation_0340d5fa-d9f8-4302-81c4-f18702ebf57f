<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Domain\Model;

/**
 * Entity object to transfer virtual route data between middlewares
 *
 * This entity encapsulates all virtual route information that gets passed
 * through the request pipeline from middleware to event listeners.
 * It replaces multiple scattered request attributes with a single structured object.
 */
class VirtualRouteContext
{
    /**
     * @var bool
     */
    private $isVirtualRoute;

    /**
     * @var array|null
     */
    private $landingPageData;

    /**
     * @var array|null
     */
    private $templatePageData;

    /**
     * @var array|null
     */
    private $flightRouteData;

    /**
     * @var string|null
     */
    private $originalPath;

    /**
     * @var string|null
     */
    private $routeSlug;

    /**
     * @var int|null
     */
    private $templatePageUid;

    /**
     * @var string|null
     */
    private $templatePagePath;

    public function __construct(
        bool $isVirtualRoute = false,
        ?array $landingPageData = null,
        ?array $templatePageData = null,
        ?array $flightRouteData = null,
        ?string $originalPath = null,
        ?string $routeSlug = null,
        ?int $templatePageUid = null,
        ?string $templatePagePath = null
    ) {
        $this->isVirtualRoute = $isVirtualRoute;
        $this->landingPageData = $landingPageData;
        $this->templatePageData = $templatePageData;
        $this->flightRouteData = $flightRouteData;
        $this->originalPath = $originalPath;
        $this->routeSlug = $routeSlug;
        $this->templatePageUid = $templatePageUid;
        $this->templatePagePath = $templatePagePath;
    }

    /**
     * Factory method for normal (non-virtual) routes
     */
    public static function createNormal(): self
    {
        return new self(false);
    }

    /**
     * Factory method for virtual routes
     */
    public static function createVirtual(
        array $landingPageData,
        array $templatePageData,
        array $flightRouteData,
        string $originalPath,
        string $routeSlug,
        int $templatePageUid,
        string $templatePagePath
    ): self {
        return new self(
            true,
            $landingPageData,
            $templatePageData,
            $flightRouteData,
            $originalPath,
            $routeSlug,
            $templatePageUid,
            $templatePagePath
        );
    }

    // Getters
    public function isVirtualRoute(): bool
    {
        return $this->isVirtualRoute;
    }

    public function getLandingPageData(): ?array
    {
        return $this->landingPageData;
    }

    public function getTemplatePageData(): ?array
    {
        return $this->templatePageData;
    }

    public function getFlightRouteData(): ?array
    {
        return $this->flightRouteData;
    }

    public function getOriginalPath(): ?string
    {
        return $this->originalPath;
    }

    public function getRouteSlug(): ?string
    {
        return $this->routeSlug;
    }

    public function getTemplatePageUid(): ?int
    {
        return $this->templatePageUid;
    }

    public function getTemplatePagePath(): ?string
    {
        return $this->templatePagePath;
    }

    /**
     * Convert to array format for backward compatibility with existing code
     * This maintains compatibility with current virtualRouteMatch array structure
     */
    public function toVirtualRouteMatch(): array
    {
        if (!$this->isVirtualRoute) {
            return [];
        }

        return [
            'landingPage' => $this->landingPageData,
            'flightRoute' => $this->flightRouteData,
            'routeSlug' => $this->routeSlug,
            'originalPath' => $this->originalPath,
            'templatePageUid' => $this->templatePageUid
        ];
    }

    /**
     * Get cache identifier for this virtual route context
     */
    public function getCacheIdentifier(): string
    {
        if (!$this->isVirtualRoute()) {
            return 'normal';
        }

        return 'virtual_' . md5($this->routeSlug . '_' . $this->landingPageData['uid']);
    }

    /**
     * Get cache tags for this virtual route context
     */
    public function getCacheTags(): array
    {
        if (!$this->isVirtualRoute()) {
            return [];
        }

        return [
            'virtual_route',
            'landing_page_' . $this->landingPageData['uid'],
            'flight_route_' . $this->flightRouteData['uid']
        ];
    }
}
