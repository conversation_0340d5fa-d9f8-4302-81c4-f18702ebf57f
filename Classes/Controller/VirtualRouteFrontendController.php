<?php
namespace Bgs\LandingPages\Controller;

use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Core\Context\Context;
use TYPO3\CMS\Core\Routing\PageArguments;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Site\Entity\SiteLanguage;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\Authentication\FrontendUserAuthentication;
use TYPO3\CMS\Frontend\Controller\TypoScriptFrontendController;
use Bgs\LandingPages\Service\PlaceholderService;

/**
 * Custom Frontend Controller for Virtual Route Pages
 * 
 * This controller extends the standard TypoScriptFrontendController to handle
 * virtual routes for Flight Landing Pages. It renders template page content
 * with placeholder replacement while maintaining all standard TYPO3 functionality.
 */
class VirtualRouteFrontendController extends TypoScriptFrontendController
{
    protected array $virtualRouteData = [];
    protected array $virtualRouteProcessedContent = [];
    protected PlaceholderService $placeholderService;

    public function __construct(
        Context $context,
        Site $site,
        SiteLanguage $siteLanguage,
        PageArguments $pageArguments,
        FrontendUserAuthentication $frontendUser
    ) {
        parent::__construct($context, $site, $siteLanguage, $pageArguments, $frontendUser);
        $this->placeholderService = GeneralUtility::makeInstance(PlaceholderService::class);
    }

    /**
     * Set virtual route data for processing
     */
    public function setVirtualRouteData(array $virtualRouteData): void
    {
        $this->virtualRouteData = $virtualRouteData;
    }

    /**
     * Get virtual route data
     */
    public function getVirtualRouteData(): array
    {
        return $this->virtualRouteData;
    }

    /**
     * Override page content generation to handle virtual routes
     */
    public function preparePageContentGeneration(ServerRequestInterface $request)
    {

        // Call parent method first to set up standard TYPO3 functionality
        parent::preparePageContentGeneration($request);

        // If this is a virtual route, process the template page data and make it available
        if (!empty($this->virtualRouteData)) {
            $this->processVirtualRouteContent();

            // Make virtual route data available to the template system
            // This ensures the data is accessible in Fluid templates and TypoScript
            $this->makeVirtualRouteDataAvailable();
        }
    }

    /**
     * Process virtual route content using template page
     */
    protected function processVirtualRouteContent(): void
    {
        $landingPage = $this->virtualRouteData['landingPage'] ?? [];
        $flightRoute = $this->virtualRouteData['flightRoute'] ?? [];

        if (empty($landingPage) || empty($flightRoute)) {
            return;
        }

        // Get template page UID from landing page
        $templatePageUid = (int)($landingPage['tx_landingpages_template_page'] ?? 0);

        if ($templatePageUid > 0) {
            // Load template page data (including title, SEO fields, etc.)
            $templatePageData = $this->getTemplatePageData($templatePageUid);

            if (!empty($templatePageData)) {
                // Process template page data with placeholders (title, SEO fields, etc.)
                $processedPageData = $this->processTemplatePageData($templatePageData, $flightRoute);

                // Load template page content elements
                $templatePageContent = $this->getTemplatePageContent($templatePageUid);

                // Process template content with placeholders
                $processedContent = $this->processTemplateContentPlaceholders($templatePageContent, $flightRoute);

                // Replace the page data and content with processed template data
                $this->replacePageWithTemplateData($processedPageData, $processedContent);
            }
        }
    }

    /**
     * Get template page data (including title, SEO fields, etc.)
     */
    protected function getTemplatePageData(int $templatePageUid): array
    {
        $queryBuilder = GeneralUtility::makeInstance(\TYPO3\CMS\Core\Database\ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $result = $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($templatePageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery();

        return $result->fetchAssociative() ?: [];
    }

    /**
     * Get template page content elements
     */
    protected function getTemplatePageContent(int $templatePageUid): array
    {
        $queryBuilder = GeneralUtility::makeInstance(\TYPO3\CMS\Core\Database\ConnectionPool::class)
            ->getQueryBuilderForTable('tt_content');

        $result = $queryBuilder
            ->select('*')
            ->from('tt_content')
            ->where(
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($templatePageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->orderBy('sorting')
            ->executeQuery();

        return $result->fetchAllAssociative();
    }

    /**
     * Process template content placeholders
     */
    protected function processTemplateContentPlaceholders(array $templateContent, array $flightRouteData): array
    {
        $processedContent = [];

        foreach ($templateContent as $contentElement) {
            $processedElement = $contentElement;

            // Process common fields that may contain placeholders
            $fieldsToProcess = ['header', 'subheader', 'bodytext', 'header_link'];

            foreach ($fieldsToProcess as $field) {
                if (isset($contentElement[$field]) && !empty($contentElement[$field])) {
                    $processedElement[$field] = $this->placeholderService->replacePlaceholders(
                        $contentElement[$field],
                        $flightRouteData
                    );
                }
            }

            $processedContent[] = $processedElement;
        }

        return $processedContent;
    }

    /**
     * Process template page data with placeholders (title, SEO fields, etc.)
     */
    protected function processTemplatePageData(array $templatePageData, array $flightRouteData): array
    {
        $processedPageData = $templatePageData;

        // Fields that may contain placeholders
        $fieldsToProcess = [
            'title', 'subtitle', 'nav_title', 'description', 'keywords',
            'seo_title', 'og_title', 'og_description', 'twitter_title', 'twitter_description'
        ];

        foreach ($fieldsToProcess as $field) {
            if (isset($templatePageData[$field]) && !empty($templatePageData[$field])) {
                $processedPageData[$field] = $this->placeholderService->replacePlaceholders(
                    $templatePageData[$field],
                    $flightRouteData
                );
            }
        }

        return $processedPageData;
    }

    /**
     * Replace page data and content with processed template data
     */
    protected function replacePageWithTemplateData(array $processedPageData, array $processedContent): void
    {
        // Replace page data with processed template page data
        foreach ($processedPageData as $field => $value) {
            $this->page[$field] = $value;
        }

        // Store the processed content for use by data processors
        $this->virtualRouteProcessedContent = $processedContent;

        // Add the processed content to the page data for template access
        $this->page['virtual_route_content'] = $processedContent;
    }

    /**
     * Replace page content with processed template content (legacy method)
     */
    protected function replacePageContentWithTemplate(array $processedContent): void
    {
        // Store the processed content for use by data processors
        $this->virtualRouteProcessedContent = $processedContent;

        // Add the processed content to the page data for template access
        $this->page['virtual_route_content'] = $processedContent;
    }

    /**
     * Get processed virtual route content
     */
    public function getVirtualRouteProcessedContent(): array
    {
        return $this->virtualRouteProcessedContent ?? [];
    }

    /**
     * Check if this is a virtual route request
     */
    public function isVirtualRoute(): bool
    {
        return !empty($this->virtualRouteData);
    }

    /**
     * Make virtual route data available to the template system
     * This ensures flight route data is accessible in Fluid templates and TypoScript
     */
    protected function makeVirtualRouteDataAvailable(): void
    {
        if (empty($this->virtualRouteData)) {
            return;
        }

        $flightRoute = $this->virtualRouteData['flightRoute'] ?? [];

        if (empty($flightRoute)) {
            return;
        }

        // Make flight route data available as template variables
        // This data will be accessible in Fluid templates as {flightRouteData}
        $flightRouteData = [
            'isVirtualRoute' => true,
            'currentFlightRoute' => $flightRoute,
            'templatePageContent' => $this->virtualRouteProcessedContent ?? []
        ];

        // Store in GLOBALS so it's available to data processors and templates
        $GLOBALS['TYPO3_CONF_VARS']['USER']['flightRouteData'] = $flightRouteData;

        // Also make it available through the content object renderer
        if ($this->cObj) {
            $this->cObj->data['flightRouteData'] = $flightRouteData;
        }
    }
}
