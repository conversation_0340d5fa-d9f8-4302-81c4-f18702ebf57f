<?php

declare(strict_types=1);

namespace Bgs\LandingPages\XmlSitemap;

use Bgs\LandingPages\Domain\Repository\FlightRouteRepository;
use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Seo\XmlSitemap\AbstractXmlSitemapDataProvider;

/**
 * Flight Routes XML Sitemap Data Provider
 *
 * Generates sitemap entries for active flight routes.
 * URLs are formed from flight route slugs.
 * Change frequency and priority are taken from the template page.
 */
class FlightRoutesXmlSitemapDataProvider extends AbstractXmlSitemapDataProvider
{
    private FlightRouteRepository $flightRouteRepository;

    public function __construct(
        ServerRequestInterface $request,
        string $name,
        array $config = [],
        ?ContentObjectRenderer $cObj = null
    ) {
        parent::__construct($request, $name, $config, $cObj);
        $this->flightRouteRepository = GeneralUtility::makeInstance(FlightRouteRepository::class);
    }

    public function getItems(): array
    {
        $items = [];
        $site = $this->getCurrentSite();

        if (!$site) {
            return $items;
        }

        // Better approach: Find all landing pages (doktype 201) with sitemap enabled
        // Then get routes for each landing page
        $landingPages = $this->getLandingPagesWithSitemapEnabled($site);

        foreach ($landingPages as $landingPage) {
            $routes = $this->flightRouteRepository->findActiveByStoragePage($landingPage['uid']);

            // Get template page for SEO settings
            $templatePage = $this->getTemplatePageForLandingPage($landingPage);

            foreach ($routes as $route) {
                try {
                    $items[] = [
                        'loc' => $this->buildVirtualRouteUrl($landingPage, $route, $site),
                        'lastMod' => time(), // Use current time since FlightRoute doesn't have timestamp
                        'changefreq' => $this->getChangeFrequency($templatePage),
                        'priority' => $this->getPriority($templatePage)
                    ];
                } catch (\Exception $e) {
                    // Skip this route if there's an error
                    continue;
                }
            }
        }

        return $items;
    }

    public function getLastModified(): int
    {
        $site = $this->getCurrentSite();
        if (!$site) {
            return 0;
        }

        $landingPages = $this->getLandingPagesWithSitemapEnabled($site);
        $lastModified = 0;

        foreach ($landingPages as $landingPage) {
            $routes = $this->flightRouteRepository->findActiveByStoragePage($landingPage['uid']);

            foreach ($routes as $route) {
                try {
                    // Use current time since FlightRoute doesn't have timestamp
                    $timestamp = time();
                    if ($timestamp > $lastModified) {
                        $lastModified = $timestamp;
                    }
                } catch (\Exception $e) {
                    // Skip this route if there's an error
                    continue;
                }
            }
        }

        return $lastModified;
    }

    public function getNumberOfPages(): int
    {
        // Return 1 to generate a single sitemap for all flight routes
        // instead of paginating them across multiple sitemaps
        $site = $this->getCurrentSite();
        if (!$site) {
            return 0;
        }

        $landingPages = $this->getLandingPagesWithSitemapEnabled($site);
        if (empty($landingPages)) {
            return 0;
        }

        // Check if we have any routes at all
        foreach ($landingPages as $landingPage) {
            $routes = $this->flightRouteRepository->findActiveByStoragePage($landingPage['uid']);
            if (!empty($routes)) {
                return 1; // Return 1 to indicate we have content for one sitemap
            }
        }

        return 0;
    }

    /**
     * Get all landing pages (doktype 201) with sitemap enabled for the current site
     */
    private function getLandingPagesWithSitemapEnabled($site): array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        // Get all landing pages in the site tree with sitemap enabled
        $siteRootPageId = $site->getRootPageId();

        return $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(201, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('tx_landingpages_enable_sitemap', $queryBuilder->createNamedParameter(1, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                // Find pages in the site tree (simple approach: check if page is under site root)
                $queryBuilder->expr()->like('slug', $queryBuilder->createNamedParameter('%'))
            )
            ->executeQuery()
            ->fetchAllAssociative();
    }

    /**
     * Get the landing page for a flight route
     */
    private function getLandingPageForRoute($route): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        return $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($route->getPid(), \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(201, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative() ?: null;
    }

    /**
     * Check if landing page should be included in sitemap
     */
    private function shouldIncludeInSitemap(array $landingPage): bool
    {
        return (bool)($landingPage['tx_landingpages_enable_sitemap'] ?? false);
    }

    /**
     * Get template page for landing page
     */
    private function getTemplatePageForLandingPage(array $landingPage): ?array
    {
        $templatePageUid = (int)($landingPage['tx_landingpages_template_page'] ?? 0);
        
        if ($templatePageUid === 0) {
            return null;
        }

        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        return $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($templatePageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(200, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative() ?: null;
    }

    /**
     * Build virtual route URL
     */
    private function buildVirtualRouteUrl(array $landingPage, $route, Site $site): string
    {
        $baseUrl = (string)$site->getBase();
        $routeSlug = $route->getRouteSlug();

        // If route slug already contains the full path, use it as-is
        // Otherwise, combine with landing page slug
        if (strpos($routeSlug, '/') === 0 || strpos($routeSlug, 'flights/') === 0) {
            // Route slug is already a full path
            return rtrim($baseUrl, '/') . '/' . ltrim($routeSlug, '/');
        } else {
            // Route slug is just the route part, combine with landing page slug
            $landingPageSlug = $landingPage['slug'] ?? '';
            return rtrim($baseUrl, '/') . $landingPageSlug . '/' . $routeSlug;
        }
    }

    /**
     * Get change frequency from template page
     */
    private function getChangeFrequency(?array $templatePage): string
    {
        if (!$templatePage) {
            return 'weekly';
        }
        
        return $templatePage['sitemap_changefreq'] ?? 'weekly';
    }

    /**
     * Get priority from template page
     */
    private function getPriority(?array $templatePage): string
    {
        if (!$templatePage) {
            return '0.5';
        }
        
        $priority = $templatePage['sitemap_priority'] ?? '0.5';
        return (string)$priority;
    }

    /**
     * Get current site from request
     */
    private function getCurrentSite(): ?Site
    {
        return $this->request->getAttribute('site');
    }
}
