<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Command;

use Bgs\LandingPages\Service\SlugUpdateService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Command to manually update child slugs for a specific page
 * 
 * This command is useful for testing the automatic slug update functionality
 * or for manually fixing slug inconsistencies.
 */
class UpdateChildSlugsCommand extends Command
{
    protected SlugUpdateService $slugUpdateService;

    public function __construct(SlugUpdateService $slugUpdateService)
    {
        $this->slugUpdateService = $slugUpdateService;
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setDescription('Update child landing page and flight route slugs for a specific page');
        $this->setHelp('This command updates all child landing pages and flight routes when a parent page slug changes.');

        $this->addArgument(
            'pageId',
            InputArgument::REQUIRED,
            'The page ID whose children should be updated'
        );

        $this->addArgument(
            'oldSlug',
            InputArgument::OPTIONAL,
            'The old slug (if not provided, will use current slug as old slug for testing)',
            ''
        );

        $this->addArgument(
            'newSlug',
            InputArgument::OPTIONAL,
            'The new slug (if not provided, will regenerate all child slugs)',
            ''
        );

        $this->addOption(
            'routes-only',
            'r',
            \Symfony\Component\Console\Input\InputOption::VALUE_NONE,
            'Update only flight routes for this landing page (not child pages)'
        );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $pageId = (int)$input->getArgument('pageId');
        $oldSlug = $input->getArgument('oldSlug');
        $newSlug = $input->getArgument('newSlug');
        $routesOnly = $input->getOption('routes-only');

        if ($pageId <= 0) {
            $io->error('Invalid page ID provided.');
            return Command::FAILURE;
        }

        // Get current page information
        $pageInfo = $this->getPageInfo($pageId);
        if (!$pageInfo) {
            $io->error(sprintf('Page with ID %d not found.', $pageId));
            return Command::FAILURE;
        }

        $io->title('Update Child Slugs Command');
        $io->note(sprintf('Processing page: %s (ID: %d)', $pageInfo['title'], $pageId));

        if ($routesOnly) {
            // Only update flight routes for this landing page
            $io->note('Mode: Update flight routes only');
            $routesUpdated = $this->slugUpdateService->updateFlightRouteSlugs($pageId);

            $io->section('Update Results');
            $io->writeln(sprintf('Flight routes updated: %d', $routesUpdated));
            $io->success('Flight route slug update completed successfully!');
            return Command::SUCCESS;
        }

        // If no old slug provided, use current slug
        if (empty($oldSlug)) {
            $oldSlug = $pageInfo['slug'];
        }

        // If no new slug provided, use current slug (for regeneration)
        if (empty($newSlug)) {
            $newSlug = $pageInfo['slug'];
        }

        $io->note(sprintf('Old slug: %s', $oldSlug));
        $io->note(sprintf('New slug: %s', $newSlug));

        // Update child slugs
        $stats = $this->slugUpdateService->updateChildSlugs($pageId, $oldSlug, $newSlug);

        // Display results
        $io->section('Update Results');
        $io->writeln(sprintf('Landing pages updated: %d', $stats['landing_pages_updated']));
        $io->writeln(sprintf('Flight routes updated: %d', $stats['flight_routes_updated']));

        if (!empty($stats['errors'])) {
            $io->section('Errors');
            foreach ($stats['errors'] as $error) {
                $io->error($error);
            }
            return Command::FAILURE;
        }

        $io->success('Child slug update completed successfully!');
        return Command::SUCCESS;
    }

    /**
     * Get page information
     * 
     * @param int $pageId Page ID
     * @return array|null Page information or null if not found
     */
    protected function getPageInfo(int $pageId): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $result = $queryBuilder
            ->select('uid', 'pid', 'title', 'slug', 'doktype')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative();

        return $result ?: null;
    }
}
