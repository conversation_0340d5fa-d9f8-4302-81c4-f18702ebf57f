<?php
namespace Bgs\LandingPages\DataProcessing;

use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\ContentObject\DataProcessorInterface;

/**
 * Data processor for virtual route data
 * 
 * This processor makes virtual route data available to Fluid templates
 * when rendering virtual routes. It only activates when virtual route
 * data is present in the GLOBALS.
 */
class VirtualRouteDataProcessor implements DataProcessorInterface
{
    /**
     * Process virtual route data and make it available to templates
     *
     * @param ContentObjectRenderer $cObj The content object renderer
     * @param array $contentObjectConfiguration The TypoScript configuration
     * @param array $processorConfiguration The processor configuration
     * @param array $processedData The processed data from previous processors
     * @return array The processed data with virtual route data added
     */
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    ): array {
        // Get the target variable name (default: flightRouteData)
        $targetVariableName = $processorConfiguration['as'] ?? 'flightRouteData';

        // Check if virtual route data is available in GLOBALS (set by VirtualRouteFrontendController)
        $virtualRouteData = $GLOBALS['TYPO3_CONF_VARS']['USER']['flightRouteData'] ?? null;

        if ($virtualRouteData !== null) {
            // Virtual route data is available - add it to processed data
            $processedData[$targetVariableName] = $virtualRouteData;
        } else {
            // No virtual route data - check if this is a normal flight landing page
            $pageData = $processedData['data'] ?? [];
            $doktype = (int)($pageData['doktype'] ?? 0);

            if ($doktype === 200) {
                // This is a normal flight landing page - provide basic structure
                $processedData[$targetVariableName] = [
                    'isVirtualRoute' => false,
                    'currentFlightRoute' => null,
                    'templatePageContent' => [],
                    'landingPageUid' => (int)($pageData['uid'] ?? 0),
                    'templatePageUid' => (int)($pageData['tx_landingpages_template_page'] ?? 0)
                ];
            } else {
                // Regular page - provide minimal structure
                $processedData[$targetVariableName] = [
                    'isVirtualRoute' => false,
                    'currentFlightRoute' => null,
                    'templatePageContent' => []
                ];
            }
        }

        return $processedData;
    }
}
