<?php
namespace Bgs\LandingPages\DataProcessing;

use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\ContentObject\DataProcessorInterface;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use Bgs\LandingPages\Domain\Repository\FlightRouteRepository;
use Bgs\LandingPages\Service\PlaceholderService;

/**
 * Data processor for Flight Landing Pages
 *
 * Provides flight route data and template page content to the page template
 */
class FlightRouteProcessor implements DataProcessorInterface
{
    protected FlightRouteRepository $flightRouteRepository;
    protected PlaceholderService $placeholderService;

    public function __construct(
        FlightRouteRepository $flightRouteRepository = null,
        PlaceholderService $placeholderService = null
    ) {
        $this->flightRouteRepository = $flightRouteRepository ?? GeneralUtility::makeInstance(FlightRouteRepository::class);
        $this->placeholderService = $placeholderService ?? GeneralUtility::makeInstance(PlaceholderService::class);
    }

    /**
     * Process data for Flight Landing Pages
     */
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    ): array {
        $targetVariableName = $cObj->stdWrapValue('as', $processorConfiguration, 'flightRouteData');

        // Get current page data
        $pageData = $processedData['data'] ?? [];
        $pageUid = (int)($pageData['uid'] ?? 0);
        $doktype = (int)($pageData['doktype'] ?? 0);

        // Only process for Flight Landing Pages (doktype 201)
        if ($doktype !== 201) {
            $processedData[$targetVariableName] = [];
            return $processedData;
        }

        // Get template page UID
        $templatePageUid = (int)($pageData['tx_landingpages_template_page'] ?? 0);

        // Get flight routes for this landing page
        $flightRoutes = [];
        if ($pageUid > 0) {
            $flightRoutesObjects = $this->flightRouteRepository->findByLandingPage($pageUid);
            // Convert objects to arrays for easier template usage
            foreach ($flightRoutesObjects as $route) {
                $flightRoutes[] = [
                    'uid' => $route->getUid(),
                    'originCode' => $route->getOriginCode(),
                    'originName' => $route->getOriginName(),
                    'originType' => $route->getOriginType(),
                    'destinationCode' => $route->getDestinationCode(),
                    'destinationName' => $route->getDestinationName(),
                    'destinationType' => $route->getDestinationType(),
                    'routeSlug' => $route->getRouteSlug(),
                    'isActive' => $route->getIsActive(),
                    'landingPage' => $route->getLandingPage()
                ];
            }
        }

        // Check if this is a virtual route from middleware
        $virtualRouteData = $this->getVirtualRouteFromRequest();
        $isVirtualRoute = !empty($virtualRouteData);

        // If it's a virtual route, use the route data from middleware
        $currentFlightRoute = null;
        $routeData = [];
        if ($isVirtualRoute) {
            $currentFlightRoute = $virtualRouteData['flightRoute'] ?? null;
            $routeData = [
                'origin' => $virtualRouteData['originCode'] ?? '',
                'destination' => $virtualRouteData['destinationCode'] ?? '',
                'routeSlug' => $virtualRouteData['routeSlug'] ?? ''
            ];
        }

        // Prepare flight data for placeholder replacement
        $flightData = [];
        if ($currentFlightRoute) {
            // Convert array data to object-like structure for buildFlightData
            if (is_array($currentFlightRoute)) {
                $flightData = $this->buildFlightDataFromArray($currentFlightRoute);
            } else {
                $flightData = $this->buildFlightData($currentFlightRoute);
            }
        }

        // Get template page content and backend layout for virtual routes
        $templatePageContent = [];
        $virtualRouteBackendLayout = '';
        $templatePageData = [];

        if ($isVirtualRoute && $currentFlightRoute && $templatePageUid > 0) {
            // Load template page data including backend layout
            $templatePageData = $this->getTemplatePageData($templatePageUid);

            // Determine backend layout using priority logic
            $virtualRouteBackendLayout = $this->determineBackendLayout($pageData, $templatePageData);

            // Get template page content
            $templatePageContent = $this->getTemplatePageContent($templatePageUid);

            // Process template content with placeholders
            $templatePageContent = $this->processTemplateContentPlaceholders($templatePageContent, $flightData);

            // Replace current page content with template content for virtual routes
            $this->replacePageContentForVirtualRoute($processedData, $templatePageContent, $virtualRouteBackendLayout);
        }

        // Assign data to template
        $processedData[$targetVariableName] = [
            'isVirtualRoute' => $isVirtualRoute,
            'currentFlightRoute' => $currentFlightRoute,
            'allFlightRoutes' => $flightRoutes,
            'templatePageContent' => $templatePageContent,
            'flightData' => $flightData,
            'routeData' => $routeData,
            'templatePageUid' => $templatePageUid,
            'templatePageData' => $templatePageData,
            'virtualRouteBackendLayout' => $virtualRouteBackendLayout,
            'landingPageUid' => $pageUid
        ];

        return $processedData;
    }



    /**
     * Build flight data array for placeholder replacement
     */
    protected function buildFlightData(array $flightRoute): array
    {
        // Build type-aware placeholders
        $originTypeAware = $this->buildTypeSpecificPlaceholder(
            $flightRoute['originName'] ?? '',
            $flightRoute['originCode'] ?? '',
            $flightRoute['originType'] ?? ''
        );
        $destinationTypeAware = $this->buildTypeSpecificPlaceholder(
            $flightRoute['destinationName'] ?? '',
            $flightRoute['destinationCode'] ?? '',
            $flightRoute['destinationType'] ?? ''
        );

        return [
            // Existing placeholders (maintain backward compatibility)
            'origin' => $flightRoute['originName'] ?? $flightRoute['originCode'],
            'destination' => $flightRoute['destinationName'] ?? $flightRoute['destinationCode'],
            'originCode' => $flightRoute['originCode'],
            'destinationCode' => $flightRoute['destinationCode'],
            'originType' => $flightRoute['originType'],
            'destinationType' => $flightRoute['destinationType'],
            'routeSlug' => $flightRoute['routeSlug'],

            // New type-aware placeholders
            'originTypeAware' => $originTypeAware,
            'destinationTypeAware' => $destinationTypeAware,

            // Mock flight data - would come from API
            'price' => '299',
            'currency' => 'EUR',
            'airline' => 'Example Airlines',
            'flight' => [
                'number' => 'EA123',
                'duration' => '2h 30m',
                'departureTime' => '10:30',
                'arrivalTime' => '13:00',
            ]
        ];
    }

    /**
     * Build type-specific placeholder value
     *
     * @param string $name Location name
     * @param string $code Location code
     * @param string $type Location type (airport, city, country)
     * @return string Formatted placeholder value
     */
    protected function buildTypeSpecificPlaceholder(string $name, string $code, string $type): string
    {
        if ($type === 'airport' && !empty($name) && !empty($code)) {
            return $name . ' (' . $code . ')';
        }

        return $name;
    }

    /**
     * Get template page content elements
     */
    protected function getTemplatePageContent(int $templatePageUid): array
    {
        $queryBuilder = GeneralUtility::makeInstance(\TYPO3\CMS\Core\Database\ConnectionPool::class)
            ->getQueryBuilderForTable('tt_content');

        $result = $queryBuilder
            ->select('*')
            ->from('tt_content')
            ->where(
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($templatePageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->orderBy('sorting')
            ->executeQuery();

        return $result->fetchAllAssociative();
    }

    /**
     * Get template page data including backend layout information
     */
    protected function getTemplatePageData(int $templatePageUid): array
    {
        $queryBuilder = GeneralUtility::makeInstance(\TYPO3\CMS\Core\Database\ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $result = $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($templatePageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery();

        return $result->fetchAssociative() ?: [];
    }

    /**
     * Determine backend layout using priority logic
     * Priority: Landing page backend_layout_next_level > Template page backend_layout
     */
    protected function determineBackendLayout(array $landingPageData, array $templatePageData): string
    {
        // First priority: Landing page "Backend Layout (Subpages)" setting
        $subpagesLayout = $landingPageData['backend_layout_next_level'] ?? '';
        if (!empty($subpagesLayout) && $subpagesLayout !== '-1') {
            return $subpagesLayout;
        }

        // Fallback: Template page "Backend Layout" setting
        $templateLayout = $templatePageData['backend_layout'] ?? '';
        if (!empty($templateLayout) && $templateLayout !== '-1') {
            return $templateLayout;
        }

        // No specific layout configured
        return '';
    }

    /**
     * Process template content placeholders
     */
    protected function processTemplateContentPlaceholders(array $templateContent, array $flightData): array
    {
        if (empty($templateContent) || empty($flightData)) {
            return $templateContent;
        }

        $processedContent = [];
        foreach ($templateContent as $contentElement) {
            $processedElement = $contentElement;

            // Process bodytext field
            if (isset($contentElement['bodytext'])) {
                $processedElement['bodytext'] = $this->placeholderService->replacePlaceholders(
                    $contentElement['bodytext'],
                    $flightData
                );
            }

            // Process header field
            if (isset($contentElement['header'])) {
                $processedElement['header'] = $this->placeholderService->replacePlaceholders(
                    $contentElement['header'],
                    $flightData
                );
            }

            // Process subheader field
            if (isset($contentElement['subheader'])) {
                $processedElement['subheader'] = $this->placeholderService->replacePlaceholders(
                    $contentElement['subheader'],
                    $flightData
                );
            }

            $processedContent[] = $processedElement;
        }

        return $processedContent;
    }

    /**
     * Replace page content with template content for virtual routes
     * This modifies the processedData to use template content instead of landing page content
     */
    protected function replacePageContentForVirtualRoute(array &$processedData, array $templateContent, string $backendLayout): void
    {
        if (empty($templateContent)) {
            return;
        }

        // Update page data to use the determined backend layout
        if (!empty($backendLayout)) {
            $processedData['data']['backend_layout'] = $backendLayout;
        }

        // Group content by colPos for proper column rendering
        $contentByColumn = [];
        foreach ($templateContent as $contentElement) {
            $colPos = (int)($contentElement['colPos'] ?? 0);
            if (!isset($contentByColumn[$colPos])) {
                $contentByColumn[$colPos] = [];
            }
            $contentByColumn[$colPos][] = $contentElement;
        }

        // Store content for use in templates
        $processedData['virtualRouteContent'] = $contentByColumn;

        // Also store the raw template content for backward compatibility
        $processedData['templateContent'] = $templateContent;
    }

    /**
     * Get virtual route data from the request (set by middleware)
     */
    protected function getVirtualRouteFromRequest(): array
    {
        $request = $GLOBALS['TYPO3_REQUEST'] ?? null;
        if (!$request) {
            return [];
        }

        return $request->getAttribute('flightLandingPages.virtualRoute', []);
    }

    /**
     * Build flight data from array (when data comes from middleware)
     */
    protected function buildFlightDataFromArray(array $flightRouteData): array
    {
        return [
            'origin' => $flightRouteData['origin_name'] ?? '',
            'originCode' => $flightRouteData['origin_code'] ?? '',
            'originType' => $flightRouteData['origin_type'] ?? '',
            'destination' => $flightRouteData['destination_name'] ?? '',
            'destinationCode' => $flightRouteData['destination_code'] ?? '',
            'destinationType' => $flightRouteData['destination_type'] ?? '',
            'routeSlug' => $flightRouteData['route_slug'] ?? '',
            'price' => '299', // Mock data - replace with real API call
            'currency' => 'EUR',
            'airline' => 'Example Airlines',
            'flight' => [
                'duration' => '2h 30m',
                'departure' => '10:00',
                'arrival' => '12:30'
            ]
        ];
    }
}
