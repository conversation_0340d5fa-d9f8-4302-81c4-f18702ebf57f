<?php
namespace Bgs\LandingPages\DataProcessing;

use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\ContentObject\DataProcessorInterface;
use Bgs\LandingPages\Domain\Repository\FlightRouteRepository;
use TYPO3\CMS\Core\Site\SiteFinder;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * DataProcessor for Destination Pairs Menu content element
 * Fetches flight routes and prepares them for display in the template
 */
class DestinationPairsMenuProcessor implements DataProcessorInterface
{
    /**
     * Process data for the Destination Pairs Menu content element
     *
     * @param ContentObjectRenderer $cObj The content object renderer
     * @param array $contentObjectConfiguration The TypoScript configuration
     * @param array $processorConfiguration The processor configuration
     * @param array $processedData The processed data to be passed to the template
     * @return array The processed data with flight routes added
     */
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    ): array {
        // Get FlightRouteRepository instance
        $flightRouteRepository = GeneralUtility::makeInstance(FlightRouteRepository::class);

        // Get storage page from content element or use current page as fallback
        $storagePid = $this->getStoragePid($processedData);

        // Set storage page for repository
        $querySettings = $flightRouteRepository->createQuery()->getQuerySettings();
        if ($storagePid > 0) {
            $querySettings->setStoragePageIds([$storagePid]);
        } else {
            // Use current page as fallback
            $querySettings->setStoragePageIds([$GLOBALS['TSFE']->id]);
        }
        $flightRouteRepository->setDefaultQuerySettings($querySettings);

        // Get all active routes
        $routes = $flightRouteRepository->findAllActive();

        // Generate full URLs for each route
        $routesWithUrls = [];
        $currentPageId = (int)$GLOBALS['TSFE']->id;

        try {
            $siteFinder = GeneralUtility::makeInstance(SiteFinder::class);
            $site = $siteFinder->getSiteByPageId($currentPageId);
            $siteBaseUrl = (string)$site->getBase();
        } catch (\Exception $e) {
            $siteBaseUrl = '';
        }

        foreach ($routes as $route) {
            $routeData = [
                'originCode' => $route->getOriginCode(),
                'originName' => $route->getOriginName(),
                'originType' => $route->getOriginType(),
                'destinationCode' => $route->getDestinationCode(),
                'destinationName' => $route->getDestinationName(),
                'destinationType' => $route->getDestinationType(),
                'routeSlug' => $route->getRouteSlug(),
                'fullUrl' => $this->generateFullUrl($siteBaseUrl, $route->getRouteSlug())
            ];
            $routesWithUrls[] = $routeData;
        }

        // Get unique origins and destinations for potential filtering
        $origins = [];
        $destinations = [];
        foreach ($routes as $route) {
            $origins[$route->getOriginCode()] = $route->getOriginName();
            $destinations[$route->getDestinationCode()] = $route->getDestinationName();
        }

        // Add data to processed data array
        $processedData['flightRoutes'] = $routesWithUrls;
        $processedData['origins'] = $origins;
        $processedData['destinations'] = $destinations;
        $processedData['storagePid'] = $storagePid;

        return $processedData;
    }

    /**
     * Get the storage page ID from the content element
     * Falls back to current page if no storage page is configured
     *
     * @param array $processedData The processed data array
     * @return int The storage page ID
     */
    protected function getStoragePid(array $processedData): int
    {
        // Check if pages field is set in content element
        if (!empty($processedData['data']['pages'])) {
            $pages = GeneralUtility::intExplode(',', $processedData['data']['pages'], true);
            if (!empty($pages)) {
                return $pages[0];
            }
        }

        // Fallback to current page
        return (int)$GLOBALS['TSFE']->id;
    }

    /**
     * Generate full URL from site base URL and route slug
     *
     * @param string $siteBaseUrl Site base URL
     * @param string $routeSlug Route slug
     * @return string Full URL
     */
    protected function generateFullUrl(string $siteBaseUrl, string $routeSlug): string
    {
        if (empty($routeSlug)) {
            return $siteBaseUrl;
        }

        // Ensure base URL ends with /
        $baseUrl = rtrim($siteBaseUrl, '/') . '/';

        // Remove leading slash from route slug if present
        $routeSlug = ltrim($routeSlug, '/');

        return $baseUrl . $routeSlug;
    }
}
