<?php
defined('TYPO3') or die();

call_user_func(static function () {
    // Register new page type icons
    $GLOBALS['TCA']['pages']['ctrl']['typeicon_classes'][200] = 'apps-pagetree-flight-template';
    $GLOBALS['TCA']['pages']['ctrl']['typeicon_classes'][201] = 'apps-pagetree-flight-landing';

    // Add new page types to doktype select
    $GLOBALS['TCA']['pages']['columns']['doktype']['config']['items'][] = [
        'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.doktype.200',
        'value' => 200,
        'icon' => 'apps-pagetree-flight-template'
    ];
    $GLOBALS['TCA']['pages']['columns']['doktype']['config']['items'][] = [
        'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.doktype.201',
        'value' => 201,
        'icon' => 'apps-pagetree-flight-landing'
    ];

    // Add new fields to pages table
    $tempColumns = [
        'tx_landingpages_template_page' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.tx_landingpages_template_page',
            'config' => [
                'type' => 'group',
                'allowed' => 'pages',
                'size' => 1,
                'maxitems' => 1,
                'minitems' => 0,
                'suggestOptions' => [
                    'default' => [
                        'additionalSearchFields' => 'title',
                        'addWhere' => ' AND pages.doktype = 200'
                    ]
                ]
            ]
        ],

        'tx_landingpages_cache_lifetime' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.tx_landingpages_cache_lifetime',
            'config' => [
                'type' => 'number',
                'size' => 10,
                'default' => 3600,
                'range' => [
                    'lower' => 0,
                    'upper' => 86400
                ]
            ]
        ],
        'tx_landingpages_enable_sitemap' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.tx_landingpages_enable_sitemap',
            'config' => [
                'type' => 'check',
                'renderType' => 'checkboxToggle',
                'items' => [
                    [
                        'label' => '',
                        'value' => '',
                    ]
                ],
                'default' => 0,
            ]
        ]
    ];

    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTCAcolumns('pages', $tempColumns);

    // Add template mappings inline field directly to pages TCA
    /*$GLOBALS['TCA']['pages']['columns']['tx_landingpages_template_mappings'] = [
        'exclude' => true,
        'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.tx_landingpages_template_mappings',
        'config' => [
            'type' => 'inline',
            'foreign_table' => 'tx_landingpages_domain_model_templatemapping',
            'foreign_field' => 'landing_page_uid',
            'maxitems' => 9,
            'appearance' => [
                'collapseAll' => 0,
                'levelLinksPosition' => 'top',
                'showSynchronizationLink' => 1,
                'showPossibleLocalizationRecords' => 1,
                'showAllLocalizationLink' => 1
            ],
        ],
    ];*/

    // Template Page Type (200) - Inherit all functionality from standard pages
    $GLOBALS['TCA']['pages']['types'][200] = $GLOBALS['TCA']['pages']['types'][\TYPO3\CMS\Core\Domain\Repository\PageRepository::DOKTYPE_DEFAULT];

    // Landing Page Type (201) - Inherit from standard page and add flight configuration
    // Start with the standard page configuration
    $GLOBALS['TCA']['pages']['types'][201] = $GLOBALS['TCA']['pages']['types'][\TYPO3\CMS\Core\Domain\Repository\PageRepository::DOKTYPE_DEFAULT];

    // Add our custom Landing Page tab to the existing configuration
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addToAllTCAtypes(
        'pages',
        '--div--;LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.tabs.landing_page,
            tx_landingpages_template_page,
            tx_landingpages_cache_lifetime,
            tx_landingpages_enable_sitemap,
            tx_landingpages_template_mappings',
        '201',
        'before:--div--;LLL:EXT:seo/Resources/Private/Language/locallang_tca.xlf:pages.tabs.seo'

    );

    //var_dump($GLOBALS['TCA']['pages']['columns']); exit;
    $GLOBALS['TCA']['pages']['columns']['tx_landingpages_template_mappings'] = [
        'exclude' => true,
        'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:pages.tx_landingpages_template_mappings',
        'config' => [
            'type' => 'inline',
            'foreign_table' => 'tx_landingpages_domain_model_templatemapping',
            'foreign_field' => 'landing_page_uid',
            'maxitems' => 9,
            'appearance' => [
                'collapseAll' => 0,
                'levelLinksPosition' => 'top',
                'showSynchronizationLink' => 1,
                'showPossibleLocalizationRecords' => 1,
                'showAllLocalizationLink' => 1
            ],
        ],
    ];

});
