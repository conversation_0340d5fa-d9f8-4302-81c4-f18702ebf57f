<?php
return [
    'ctrl' => [
        'title' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute',
        'label' => 'route_slug',
        'label_alt' => 'origin_code,destination_code',
        'label_alt_force' => true,
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'delete' => 'deleted',
        'enablecolumns' => [
            'starttime' => 'starttime',
            'endtime' => 'endtime',
        ],
        'searchFields' => 'origin_code,origin_name,destination_code,destination_name,route_slug',
        'iconfile' => 'EXT:landing-pages/Resources/Public/Icons/tx_flightlandingpages_domain_model_flightroute.svg'
    ],
    'types' => [
        '1' => ['showitem' => '--palette--;;origin_palette, --palette--;;destination_palette, route_slug, --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:tabs.access, is_active, starttime, endtime'],
    ],
    'palettes' => [
        'origin_palette' => [
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute.origin',
            'showitem' => 'origin_type, origin_code, origin_name',
        ],
        'destination_palette' => [
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute.destination',
            'showitem' => 'destination_type, destination_code, destination_name',
        ],
    ],
    'columns' => [
        'starttime' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.starttime',
            'config' => [
                'type' => 'datetime',
                'default' => 0,
                'behaviour' => [
                    'allowLanguageSynchronization' => true
                ]
            ],
        ],
        'endtime' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.endtime',
            'config' => [
                'type' => 'datetime',
                'default' => 0,
                'range' => [
                    'upper' => mktime(0, 0, 0, 1, 1, 2038)
                ],
                'behaviour' => [
                    'allowLanguageSynchronization' => true
                ]
            ],
        ],
        'origin_code' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute.origin_code',
            'config' => [
                'type' => 'input',
                'size' => 10,
                'eval' => 'trim,upper',
                'required' => true,
                'max' => 10,
                'placeholder' => 'BER',
            ],
        ],
        'origin_name' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute.origin_name',
            'config' => [
                'type' => 'input',
                'size' => 25,
                'eval' => 'trim',
                'required' => true,
                'max' => 255,
                'placeholder' => 'Berlin Brandenburg Airport',
            ],
        ],
        'origin_type' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute.origin_type',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'items' => [
                    ['label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute.origin_type.airport', 'value' => 'airport'],
                    ['label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute.origin_type.city', 'value' => 'city'],
                    ['label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute.origin_type.country', 'value' => 'country'],
                ],
                'default' => 'airport',
            ],
        ],
        'destination_code' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute.destination_code',
            'config' => [
                'type' => 'input',
                'size' => 10,
                'eval' => 'trim,upper',
                'required' => true,
                'max' => 10,
                'placeholder' => 'SOF',
            ],
        ],
        'destination_name' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute.destination_name',
            'config' => [
                'type' => 'input',
                'size' => 25,
                'eval' => 'trim',
                'required' => true,
                'max' => 255,
                'placeholder' => 'Sofia Airport',
            ],
        ],
        'destination_type' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute.destination_type',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'items' => [
                    ['label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute.destination_type.airport', 'value' => 'airport'],
                    ['label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute.destination_type.city', 'value' => 'city'],
                    ['label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightroute.destination_type.country', 'value' => 'country'],
                ],
                'default' => 'airport',
            ],
        ],
        'route_slug' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_flightlandingpages_domain_model_flightroute.route_slug',
            'config' => [
                'type' => 'slug',
                'size' => 50,
                'generatorOptions' => [
                    'fields' => [
                        'origin_code',
                        'destination_code',
                    ],
                    'fieldSeparator' => '-',
                    'prefixParentPageSlug' => false,
                    'replacements' => [
                        '/' => '-',
                    ],
                    'postModifiers' => [
                        'Bgs\\LandingPages\\UserFunctions\\SlugModifier->addParentPagePath',
                    ],
                ],
                'fallbackCharacter' => '-',
                'eval' => 'uniqueInSite',
                'default' => '',
                'placeholder' => 'ber-sof',
            ],
        ],

        'is_active' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_flightlandingpages_domain_model_flightroute.is_active',
            'config' => [
                'type' => 'check',
                'renderType' => 'checkboxToggle',
                'items' => [
                    [
                        'label' => '',
                        'value' => '',
                    ]
                ],
                'default' => 1,
            ],
        ],
    ],
];
