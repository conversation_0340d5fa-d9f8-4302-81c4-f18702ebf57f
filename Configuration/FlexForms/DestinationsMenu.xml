<T3DataStructure>
    <sheets>
        <sDEF>
            <ROOT>
                <TCEforms>
                    <sheetTitle>LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang.xlf:flexform.destinations_menu.general</sheetTitle>
                </TCEforms>
                <type>array</type>
                <el>
                    <settings.displayMode>
                        <TCEforms>
                            <label>LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang.xlf:flexform.destinations_menu.display_mode</label>
                            <config>
                                <type>select</type>
                                <renderType>selectSingle</renderType>
                                <items>
                                    <numIndex index="0">
                                        <label>LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang.xlf:flexform.destinations_menu.display_mode.list</label>
                                        <value>list</value>
                                    </numIndex>
                                    <numIndex index="1">
                                        <label>LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang.xlf:flexform.destinations_menu.display_mode.grid</label>
                                        <value>grid</value>
                                    </numIndex>
                                    <numIndex index="2">
                                        <label>LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang.xlf:flexform.destinations_menu.display_mode.cards</label>
                                        <value>cards</value>
                                    </numIndex>
                                </items>
                                <default>list</default>
                            </config>
                        </TCEforms>
                    </settings.displayMode>
                    <settings.showOriginFilter>
                        <TCEforms>
                            <label>LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang.xlf:flexform.flight_reference.show_origin_filter</label>
                            <config>
                                <type>check</type>
                                <renderType>checkboxToggle</renderType>
                                <items>
                                    <numIndex index="0">
                                        <label></label>
                                        <value>1</value>
                                    </numIndex>
                                </items>
                                <default>0</default>
                            </config>
                        </TCEforms>
                    </settings.showOriginFilter>
                    <settings.showDestinationFilter>
                        <TCEforms>
                            <label>LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang.xlf:flexform.flight_reference.show_destination_filter</label>
                            <config>
                                <type>check</type>
                                <renderType>checkboxToggle</renderType>
                                <items>
                                    <numIndex index="0">
                                        <label></label>
                                        <value>1</value>
                                    </numIndex>
                                </items>
                                <default>0</default>
                            </config>
                        </TCEforms>
                    </settings.showDestinationFilter>
                    <settings.itemsPerPage>
                        <TCEforms>
                            <label>LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang.xlf:flexform.flight_reference.items_per_page</label>
                            <config>
                                <type>input</type>
                                <eval>int,trim</eval>
                                <default>10</default>
                                <range>
                                    <lower>1</lower>
                                    <upper>100</upper>
                                </range>
                            </config>
                        </TCEforms>
                    </settings.itemsPerPage>
                </el>
            </ROOT>
        </sDEF>
    </sheets>
</T3DataStructure>
