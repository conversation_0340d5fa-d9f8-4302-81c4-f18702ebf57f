<?php

/**
 * Backend routes for Flight Landing Pages extension
 */
return [
    'flight_landing_pages_csv_export' => [
        'path' => '/flight-landing-pages/csv-export',
        'target' => \Bgs\LandingPages\Controller\Backend\CsvExportController::class . '::exportAction'
    ],
    'flight_landing_pages_csv_import_form' => [
        'path' => '/flight-landing-pages/csv-import-form',
        'target' => \Bgs\LandingPages\Controller\Backend\CsvExportController::class . '::importFormAction'
    ],
    'flight_landing_pages_csv_import' => [
        'path' => '/flight-landing-pages/csv-import',
        'target' => \Bgs\LandingPages\Controller\Backend\CsvExportController::class . '::importAction'
    ],
];
