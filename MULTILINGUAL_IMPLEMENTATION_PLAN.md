# Flight Landing Pages - Multilingual Implementation Plan

## Overview

This document outlines the step-by-step implementation plan for adding multilingual support to the Flight Landing Pages extension. The implementation follows TYPO3 v12.04 best practices for a clean, modern multilingual architecture.

**Difficulty Score: 5/10** (Reduced complexity without backward compatibility constraints)

## Documentation References

- [TYPO3 TCA Language Fields](https://docs.typo3.org/m/typo3/reference-tca/12.4/en-us/BestPractises/LanguageFields.html) - Core reference for implementing translation fields
- [TYPO3 Frontend Localization Guide](https://docs.typo3.org/m/typo3/guide-frontendlocalization/main/en-us/CoreSupport/Tca/Index.html) - Complete localization implementation guide
- [TYPO3 XML Sitemap Documentation](https://docs.typo3.org/m/typo3/reference-coreapi/main/en-us/ApiOverview/Seo/XmlSitemap.html) - XML sitemap provider implementation
- [TYPO3 Database Schema](https://docs.typo3.org/m/typo3/reference-coreapi/main/en-us/ExtensionArchitecture/FileStructure/Index.html) - Extension file structure and database schema
- [TYPO3 Extbase Localization](https://docs.typo3.org/m/typo3/reference-coreapi/main/en-us/ExtensionArchitecture/Extbase/Reference/Domain/Model/Localization.html) - Extbase domain model localization
- [TYPO3 Site Configuration](https://docs.typo3.org/m/typo3/reference-coreapi/main/en-us/ApiOverview/SiteHandling/Index.html) - Site and language configuration

## Implementation Steps

### Step 1: Database Schema Extension
**Motivation**: Add TYPO3 standard translation fields to enable the translation system from the start with clean architecture.

**Files to modify:**
- `ext_tables.sql`

**Tasks:**
1. Add `sys_language_uid` field (language identifier)
2. Add `l10n_parent` field (reference to default language record)
3. Add `l10n_source` field (reference to translation source)
4. Add `l10n_diffsource` field (diff tracking for translations)
5. Add database indexes for performance
6. Test database upgrade process

**Validation:**
- Database schema creates successfully
- Translation fields are properly configured
- Extension installs cleanly on fresh systems

**Dependencies:**
- None (foundation step)

### Step 2: TCA Configuration for Translation
**Motivation**: Configure TYPO3's Table Configuration Array to enable translation interface and define which fields are translatable.

**Files to modify:**
- `Configuration/TCA/tx_flightlandingpages_domain_model_flightroute.php`

**Tasks:**
1. Add translation control fields to `ctrl` section
2. Define language field configurations
3. Set `l10n_mode` for translatable fields (`origin_name`, `destination_name`)
4. Set `l10n_mode = 'exclude'` for non-translatable fields (codes, slugs)
5. Add language palette to form layout
6. Update showitem configuration to include language tab

**Validation:**
- Backend shows translation interface
- Only name fields are translatable
- Language selector appears in record forms
- Translation records can be created
- Non-translatable fields are properly excluded

**Dependencies:**
- Step 1 (Database Schema Extension)

### Step 3: Domain Model Updates
**Motivation**: Design Extbase domain model with native multilingual support and language-aware queries from the ground up.

**Files to modify:**
- `Classes/Domain/Model/FlightRoute.php`
- `Classes/Domain/Repository/FlightRouteRepository.php`

**Tasks:**
1. Add language-related properties to domain model
2. Add getters/setters for language fields
3. Create language-aware repository methods
4. Implement translation fallback logic
5. Add method to get translated route data

**Validation:**
- Repository can query by language efficiently
- Translation fallback works correctly
- Domain model is clean and well-structured
- Language-aware queries return correct results

**Dependencies:**
- Step 1 (Database Schema Extension)
- Step 2 (TCA Configuration)

### Step 4: Placeholder Service Enhancement
**Motivation**: Build placeholder service with native multilingual support and intelligent fallback mechanisms.

**Files to modify:**
- `Classes/Service/PlaceholderService.php`

**Tasks:**
1. Design placeholder replacement methods with language-first approach
2. Implement efficient translation lookup logic
3. Add intelligent fallback mechanism for missing translations
4. Build placeholder replacement with multilingual architecture
5. Optimize for performance with caching strategies

**Validation:**
- Placeholders show translated content in correct language
- Fallback to default language works seamlessly
- Performance is optimized for multilingual scenarios

**Dependencies:**
- Step 3 (Domain Model Updates)

### Step 5: Backend Interface Language Support
**Motivation**: Provide convenient backend interface for managing translations without requiring users to understand TYPO3's translation system deeply.

**Files to modify:**
- `Classes/Controller/Backend/FlightRouteController.php`
- Backend templates

**Tasks:**
1. Add language selector to backend list view
2. Filter routes by selected language
3. Show translation status indicators
4. Add quick translation creation buttons
5. Update pagination to be language-aware

**Validation:**
- Backend shows language-specific route lists
- Translation status is clearly visible
- Users can easily create translations

**Dependencies:**
- Step 2 (TCA Configuration)
- Step 3 (Domain Model Updates)

### Step 6: CSV Export Enhancement
**Motivation**: Design CSV export with native multilingual support for efficient translation management.

**Files to modify:**
- `Classes/Controller/Backend/CsvExportController.php`

**Tasks:**
1. Design CSV format with clear language column structure
2. Export default language and all translations efficiently
3. Create intuitive format for translation management
4. Add clear language indicators in exported data
5. Handle missing translations gracefully with clear indicators

**Validation:**
- CSV export includes all language versions efficiently
- Export format is intuitive for translators
- Missing translations are clearly indicated

**Dependencies:**
- Step 3 (Domain Model Updates)

### Step 7: CSV Import Enhancement
**Motivation**: Build robust CSV import system designed for efficient multilingual data management.

**Files to modify:**
- `Classes/Controller/Backend/CsvExportController.php`
- Backend import templates

**Tasks:**
1. Detect language columns in import CSV
2. Create/update translation records during import
3. Validate translation data consistency
4. Handle duplicate detection across languages
5. Provide import feedback for translations

**Validation:**
- Import creates translation records correctly
- Validation prevents inconsistent data
- Import feedback shows translation status

**Dependencies:**
- Step 6 (CSV Export Enhancement)
- Step 3 (Domain Model Updates)

### Step 8: XML Sitemap Multilingual Support
**Motivation**: Generate separate sitemap entries for each language version to improve SEO for multilingual sites.

**Files to modify:**
- `Classes/XmlSitemap/FlightRoutesXmlSitemapDataProvider.php`

**Tasks:**
1. Detect all configured site languages
2. Generate sitemap entries for each language
3. Use language-specific URLs
4. Include hreflang attributes if needed
5. Maintain performance with language-aware queries

**Validation:**
- Sitemap includes all language versions
- URLs are correctly formatted for each language
- Performance remains acceptable

**Dependencies:**
- Step 3 (Domain Model Updates)

### Step 9: Frontend Rendering Updates
**Motivation**: Ensure frontend rendering uses correct language content based on current site language.

**Files to modify:**
- `Classes/DataProcessing/VirtualRouteDataProcessor.php`
- `Classes/Middleware/VirtualRouteMiddleware.php`

**Tasks:**
1. Detect current frontend language
2. Load translated route data in data processors
3. Update virtual route middleware for language handling
4. Ensure placeholder replacement uses correct language
5. Maintain caching efficiency

**Validation:**
- Frontend shows translated content
- Language switching works correctly
- Caching works properly across languages

**Dependencies:**
- Step 4 (Placeholder Service Enhancement)
- Step 3 (Domain Model Updates)

### Step 10: Testing and Documentation
**Motivation**: Ensure reliability and provide clear guidance for users implementing multilingual flight routes.

**Files to create/modify:**
- Test files
- Documentation files
- Migration guide

**Tasks:**
1. Create unit tests for translation functionality
2. Create functional tests for multilingual scenarios
3. Write user documentation for translation workflow
4. Create migration guide for existing installations
5. Document best practices for multilingual setup

**Validation:**
- All tests pass
- Documentation is clear and complete
- Migration process is well-documented

**Dependencies:**
- All previous steps (comprehensive testing)

## Implementation Strategy

### For Fresh Installations:
1. **Clean Architecture**: Build multilingual support from the ground up
2. **Optimal Design**: No legacy constraints allow for best practices
3. **Performance**: Design with multilingual performance in mind from start
4. **Testing**: Comprehensive testing without compatibility concerns

## Success Criteria

- ✅ No duplication of route records
- ✅ Only name fields are translatable
- ✅ Default language fallback works
- ✅ Export/import supports translations
- ✅ Placeholders work with selected language
- ✅ Backend interface is user-friendly
- ✅ Automatic slug updates continue working
- ✅ XML sitemaps include all language versions
- ✅ Clean, modern multilingual architecture

## Risk Assessment

**Low Risk:**
- Database schema design (clean implementation)
- TCA configuration (standard TYPO3 approach)
- Domain model design (no legacy constraints)

**Medium Risk:**
- CSV import/export functionality (data integrity important)
- Frontend rendering optimization (performance considerations)

**Low-Medium Risk:**
- XML sitemap generation (straightforward multilingual implementation)

## Implementation Order Validation

The steps are ordered to ensure:
1. **Foundation First**: Database and TCA changes provide the base
2. **Core Logic**: Domain model and services build on the foundation
3. **User Interface**: Backend and import/export enhance usability
4. **Frontend Integration**: Virtual routes and sitemaps complete the feature
5. **Quality Assurance**: Testing and documentation ensure reliability

Each step builds on previous steps and can be tested independently.

## Timeline Estimate

- **Steps 1-3**: 1-2 days (clean foundation without compatibility concerns)
- **Steps 4-6**: 2-3 days (backend functionality)
- **Steps 7-8**: 2-3 days (import/export and SEO)
- **Steps 9-10**: 2-3 days (frontend and testing)

**Total**: 7-11 days for complete implementation (reduced due to no backward compatibility)

## Additional Considerations

### Performance Impact
- Clean multilingual design optimized from start
- Proper indexing ensures excellent performance
- Caching strategies designed for multilingual scenarios

### Maintenance
- Modern TYPO3 translation system with clean architecture
- Well-documented code with no legacy constraints
- Follows latest TYPO3 v12.04 coding standards and conventions

### Benefits of No Backward Compatibility
- **Cleaner Code**: No legacy workarounds or compatibility layers
- **Better Performance**: Optimized for multilingual from the ground up
- **Simpler Testing**: No need to test legacy scenarios
- **Modern Architecture**: Can use latest TYPO3 v12.04 features without constraints
- **Faster Development**: No time spent on compatibility considerations
