<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Pagination Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="Resources/Public/CSS/backend.css">
</head>
<body>
    <div class="container mt-4">
        <div class="flight-landing-page-preview">
            <div class="alert alert-info mt-3">
                <div class="media">
                    <div class="media-body">
                        <div class="d-flex align-items-center mb-2">
                            <h5 class="mt-0 mb-0">
                                Destination Pairs
                                <span class="badge badge-secondary">50</span>
                                <span class="badge badge-success">45 Active</span>
                                <span class="badge badge-warning">5 Inactive</span>
                            </h5>
                            <div class="flex-grow-1"></div>
                            <div class="backend-search-input mr-3">
                                <div class="input-group input-group-sm">
                                    <input type="text" id="backend-route-search" class="form-control form-control-sm" placeholder="Search...">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary btn-sm" type="button" id="clear-backend-search" title="Clear search" style="display: none;">×</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <small class="text-muted mb-2 d-block">
                            <span id="backend-results-count"></span>
                        </small>

                        <div class="flight-routes-list mt-3">
                            <!-- Generate 50 test items -->
                            <div class="flight-route-item" data-search-content="BER SOF Berlin Sofia /ber-sof">
                                <div class="route-actions">
                                    <div class="btn-group" role="group">
                                        <a href="#" class="btn btn-default btn-sm">👁</a>
                                        <a href="#" class="btn btn-default btn-sm">✏</a>
                                    </div>
                                </div>
                                <div class="route-codes">
                                    <span class="badge badge-primary">BER</span>
                                    <span class="route-arrow">→</span>
                                    <span class="badge badge-success">SOF</span>
                                </div>
                                <div class="route-names">
                                    <span class="text-muted">Berlin → Sofia</span>
                                </div>
                                <div class="route-slug">
                                    <small class="text-muted">
                                        <code>/ber-sof</code>
                                    </small>
                                </div>
                            </div>

                            <div class="flight-route-item" data-search-content="BER VAR Berlin Varna /ber-var">
                                <div class="route-actions">
                                    <div class="btn-group" role="group">
                                        <a href="#" class="btn btn-default btn-sm">👁</a>
                                        <a href="#" class="btn btn-default btn-sm">✏</a>
                                    </div>
                                </div>
                                <div class="route-codes">
                                    <span class="badge badge-primary">BER</span>
                                    <span class="route-arrow">→</span>
                                    <span class="badge badge-success">VAR</span>
                                </div>
                                <div class="route-names">
                                    <span class="text-muted">Berlin → Varna</span>
                                </div>
                                <div class="route-slug">
                                    <small class="text-muted">
                                        <code>/ber-var</code>
                                    </small>
                                </div>
                            </div>

                            <div class="flight-route-item" data-search-content="FRA SOF Frankfurt Sofia /fra-sof">
                                <div class="route-actions">
                                    <div class="btn-group" role="group">
                                        <a href="#" class="btn btn-default btn-sm">👁</a>
                                        <a href="#" class="btn btn-default btn-sm">✏</a>
                                    </div>
                                </div>
                                <div class="route-codes">
                                    <span class="badge badge-primary">FRA</span>
                                    <span class="route-arrow">→</span>
                                    <span class="badge badge-success">SOF</span>
                                </div>
                                <div class="route-names">
                                    <span class="text-muted">Frankfurt → Sofia</span>
                                </div>
                                <div class="route-slug">
                                    <small class="text-muted">
                                        <code>/fra-sof</code>
                                    </small>
                                </div>
                            </div>

                            <!-- Add more test items programmatically -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Generate more test data
        const routesList = document.querySelector('.flight-routes-list');
        const cities = [
            {code: 'BER', name: 'Berlin'},
            {code: 'FRA', name: 'Frankfurt'},
            {code: 'MUC', name: 'Munich'},
            {code: 'SOF', name: 'Sofia'},
            {code: 'VAR', name: 'Varna'},
            {code: 'BOJ', name: 'Burgas'},
            {code: 'PDV', name: 'Plovdiv'},
            {code: 'LON', name: 'London'},
            {code: 'PAR', name: 'Paris'},
            {code: 'ROM', name: 'Rome'},
            {code: 'MAD', name: 'Madrid'},
            {code: 'BCN', name: 'Barcelona'},
            {code: 'VIE', name: 'Vienna'},
            {code: 'ZUR', name: 'Zurich'},
            {code: 'AMS', name: 'Amsterdam'}
        ];

        // Generate 47 more items (we already have 3)
        for (let i = 0; i < 47; i++) {
            const origin = cities[Math.floor(Math.random() * cities.length)];
            const destination = cities[Math.floor(Math.random() * cities.length)];
            
            if (origin.code !== destination.code) {
                const slug = `${origin.code.toLowerCase()}-${destination.code.toLowerCase()}`;
                const isActive = Math.random() > 0.1; // 90% active
                
                const item = document.createElement('div');
                item.className = `flight-route-item${isActive ? '' : ' route-inactive'}`;
                item.setAttribute('data-search-content', `${origin.code} ${destination.code} ${origin.name} ${destination.name} /${slug}`);
                item.setAttribute('data-route-active', isActive);
                
                item.innerHTML = `
                    <div class="route-actions">
                        <div class="btn-group" role="group">
                            <a href="#" class="btn btn-default btn-sm">👁</a>
                            <a href="#" class="btn btn-default btn-sm">✏</a>
                        </div>
                    </div>
                    <div class="route-codes">
                        <span class="badge badge-primary">${origin.code}</span>
                        <span class="route-arrow">→</span>
                        <span class="badge badge-success">${destination.code}</span>
                    </div>
                    <div class="route-names">
                        <span class="text-muted">${origin.name} → ${destination.name}</span>
                        ${!isActive ? '<span class="badge badge-warning ml-2">Inactive</span>' : ''}
                    </div>
                    <div class="route-slug">
                        <small class="text-muted">
                            <code>/${slug}</code>
                        </small>
                    </div>
                `;
                
                routesList.appendChild(item);
            }
        }
    </script>
    
    <script src="Resources/Public/JavaScript/backend.js"></script>
</body>
</html>
